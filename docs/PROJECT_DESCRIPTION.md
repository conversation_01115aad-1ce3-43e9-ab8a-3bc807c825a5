# YT Subs & Summarization API

## 📋 Обзор проекта

**YT Subs & Summarization API** — это высокопроизводительный асинхронный веб-сервис на Python, предназначенный для:

1. **Извлечения субтитров с YouTube-видео** с помощью `yt-dlp`
2. **Суммаризации текстового контента** с использованием Google Gemini API

Проект построен с акцентом на скорость, надежность и масштабируемость, используя современный асинхронный стек технологий.

## 🏗️ Архитектура

### Основные технологии:
- **FastAPI** — сверхбыстрый веб-фреймворк для создания API
- **ARQ (Asynchronous Redis Queue)** — асинхронная очередь задач
- **SQLModel** — объединение Pydantic и SQLAlchemy для работы с данными
- **PostgreSQL** — основная база данных
- **Redis** — брокер сообщений для очереди задач
- **Google Gemini API** — для суммаризации текста
- **yt-dlp** — для извлечения субтитров YouTube
- **Loguru** — структурированное логирование

### Принципы архитектуры:
1. **Полная асинхронность** — все операции используют `async`/`await`
2. **Очередь задач** — тяжелые операции выполняются в фоновых воркерах ARQ
3. **Интеллектуальное кэширование** — результаты сохраняются в PostgreSQL
4. **Единый источник правды** — SQLModel для моделей БД, валидации и API схем

## 📁 Структура проекта

```
yt_subs_api18/
├── app/
│   ├── core/                    # Основные настройки
│   │   ├── config.py           # Конфигурация через Pydantic Settings
│   │   ├── db.py               # Подключение к БД и Redis
│   │   ├── middleware.py       # Rate limiting, CORS
│   │   └── logging_config.py   # Настройка логирования
│   ├── features/               # Основная бизнес-логика
│   │   ├── subtitles/          # Модуль субтитров
│   │   │   ├── api.py         # FastAPI роутеры
│   │   │   ├── models.py      # SQLModel модели БД
│   │   │   ├── schemas.py     # Pydantic схемы для API
│   │   │   └── service.py     # Бизнес-логика
│   │   ├── summarization/      # Модуль суммаризации
│   │   └── tasks/             # Управление задачами
│   ├── tasks/                  # ARQ задачи
│   │   ├── subtitles.py       # Задача загрузки субтитров
│   │   ├── summarization.py   # Задача суммаризации
│   │   ├── subs_utils/        # Утилиты для работы с субтитрами
│   │   └── summ_utils/        # Утилиты для суммаризации
│   ├── main.py                # Главное FastAPI приложение
│   └── worker.py              # Настройка ARQ воркера
├── client/                    # CLI клиенты
│   ├── run_summary.py         # Основной клиент суммаризации
│   ├── run_summary_alt_ui.py  # Клиент с улучшенным UI
│   └── summarize_modules/     # Модули клиента
├── runners/                   # Запуск компонентов
│   └── run_app_runner.py      # Unified runner для API и воркеров
├── migrations/                # Alembic миграции БД
├── docker-compose.yml         # PostgreSQL + Redis
└── pyproject.toml            # Зависимости и конфигурация
```

## 🌟 Основные возможности

### 1. Извлечение субтитров YouTube
- **Endpoint**: `POST /subtitles/`
- Быстрое получение субтитров для любого YouTube-видео
- Поддержка автоматических и ручных субтитров
- Интеллектуальное кэширование для мгновенного доступа
- Очистка и парсинг TTML/JSON3 форматов

### 2. Суммаризация текста
- **Endpoint**: `POST /summarize/`
- Глубокая суммаризация с помощью Google Gemini API
- Поддержка различных режимов суммаризации
- Кэширование результатов с хэшированием текста
- Поддержка больших текстов с разбиением на части

### 3. Управление задачами
- **Endpoints**: `/tasks/{task_id}/status`, `/tasks/{task_id}/result`
- Real-time отслеживание статуса через Server-Sent Events (SSE)
- Асинхронная обработка в фоновых воркерах
- Надежная обработка ошибок и повторные попытки

## 🚀 Установка и запуск

### Предварительные требования:
- Python 3.12+
- Docker & Docker Compose
- `uv` (современный менеджер пакетов Python)

### Быстрый старт:

1. **Клонирование репозитория:**
```bash
git clone <repository-url>
cd yt_subs_api18
```

2. **Настройка переменных окружения:**
```bash
cp .env.example .env
# Отредактируйте .env файл с вашими настройками
```

3. **Запуск инфраструктуры:**
```bash
docker-compose up -d  # PostgreSQL + Redis
```

4. **Установка зависимостей:**
```bash
uv sync
```

5. **Создание БД и миграции:**
```bash
uv run scripts/create_db.py
alembic upgrade head
```

6. **Запуск сервера и воркеров:**
```bash
uv run start_server
# или
uv run runners/run_app_runner.py
```

API будет доступен по адресу: `http://localhost:8001`
Документация Swagger: `http://localhost:8001/docs`

## 💻 CLI клиенты

Проект включает удобные консольные утилиты для работы с API суммаризации:

```bash
# Основной клиент
uv run summaries --directory ./texts --mode default --api-key YOUR_KEY

# Клиент с улучшенным UI
uv run summaries_alt --directory ./texts --mode default --api-key YOUR_KEY
```

## 🔧 Конфигурация

Основные переменные окружения:
- `DATABASE_URL` — URL подключения к PostgreSQL
- `REDIS_URL` — URL подключения к Redis
- `GEMINI_API_KEY` — ключ API Google Gemini
- `SOCKS5_PROXY_GEMINI` — прокси для Gemini API (опционально)
- `SOCKS5_PROXY_YOUTUBE` — прокси для YouTube (опционально)
- `YOUTUBE_COOKIES_FILE` — файл с куками для yt-dlp (опционально)

## 🎯 Особенности реализации

1. **Полная асинхронность** — все I/O операции неблокирующие
2. **Graceful degradation** — система продолжает работать при недоступности внешних сервисов
3. **Rate limiting** — защита от перегрузки через SlowAPI
4. **Comprehensive logging** — детальное логирование всех операций
5. **Health checks** — проверка доступности внешних ресурсов при старте
6. **Модульная архитектура** — легкое добавление новых функций

## 📊 Технические характеристики

### Производительность:
- Асинхронная обработка тысяч запросов в секунду
- Интеллектуальное кэширование снижает нагрузку на внешние API
- Пул соединений с базой данных для оптимизации
- Rate limiting для защиты от перегрузки

### Надежность:
- Graceful handling всех внешних зависимостей
- Автоматические повторные попытки для неустойчивых операций
- Comprehensive error handling и логирование
- Health checks при запуске компонентов

### Масштабируемость:
- Горизонтальное масштабирование воркеров ARQ
- Stateless API сервер
- Кэширование в PostgreSQL
- Поддержка прокси для работы в различных сетевых условиях

Этот проект представляет собой современное, производительное и масштабируемое решение для работы с YouTube субтитрами и суммаризацией текста, построенное на передовых технологиях Python экосистемы.
