Отличная задача! Разработка элегантной, современной и масштабируемой архитектуры — это ключ к успеху проекта. Я подготовил для вас пошаговый план, ориентированный на простоту, использование современных и популярных в 2024-2025 годах технологий, а также на удобство для разработчика уровня junior.

### **Концепция архитектуры: The "Modern Async Stack"**

Мы построим систему на базе асинхронного фреймворка **FastAPI**, который является одним из самых быстрых и популярных решений для создания API на Python. Для обработки "тяжелых" задач, таких как скачивание видео и запросы к Gemini, мы будем использовать асинхронную очередь задач **ARQ (Asynchronous Redis Queue)**, которая идеально и просто интегрируется с FastAPI. В качестве базы данных мы выберем **PostgreSQL**, а для взаимодействия с ней — **SQLModel**, который элегантно объединяет модели данных API и БД.

**Ключевые компоненты стека:**

*   **Веб-фреймворк:** **FastAPI** — за высокую производительность, асинхронность, автоматическую документацию и простую валидацию данных.
*   **Очередь задач:** **ARQ (Asynchronous Redis Queue)** — за простоту, нативную поддержку `asyncio` и идеальную совместимость с FastAPI.
*   **Брокер сообщений:** **Redis** — как быстрая и надежная основа для очереди задач ARQ.
*   **База данных:** **PostgreSQL** — за надежность и функциональность.
*   **ORM / Работа с БД:** **SQLModel** — для упрощения работы с моделями данных, объединяя Pydantic и SQLAlchemy. Работает асинхронно через драйвер **asyncpg**.
*   **Логирование:** **Loguru** — для простого, но мощного и красиво форматированного логирования.
*   **Клиент:** **HTTPX** и **Typer** — для создания простого и современного асинхронного клиента.

---

### **Пошаговый план реализации с галочками**

Этот план разбит на логические этапы, чтобы junior-разработчик мог последовательно двигаться от настройки окружения до готового приложения.

#### **Этап 1: ⚙️ Настройка окружения и основных зависимостей**

*   [ ] **1. Установить Python:** Убедитесь, что установлена версия Python 3.9 или выше.
*   [ ] **2. Создать и активировать виртуальное окружение:**
    ```bash
    python -m venv venv
    source venv/bin/activate  # Для Windows: venv\Scripts\activate
    ```
*   [ ] **3. Установить Docker и Docker Compose:** Это самый простой способ запустить PostgreSQL и Redis без установки на основную систему.
*   [ ] **4. Создать файл `docker-compose.yml`:**
    ```yaml
    version: '3.8'
    services:
      postgres:
        image: postgres:15
        environment:
          POSTGRES_USER: user
          POSTGRES_PASSWORD: password
          POSTGRES_DB: app_db
        ports:
          - "5432:5432"
        volumes:
          - postgres_data:/var/lib/postgresql/data/
      redis:
        image: redis:7
        ports:
          - "6379:6379"
    volumes:
      postgres_data:
    ```
*   [ ] **5. Запустить сервисы:** В терминале, в той же папке, где находится `docker-compose.yml`, выполните:
    ```bash
    docker-compose up -d
    ```
*   [ ] **6. Установить основные библиотеки Python:**
    ```bash
    pip install "fastapi[all]" "sqlmodel" "asyncpg" "arq" "yt-dlp" "google-generativeai" "loguru" "python-dotenv"
    ```

#### **Этап 2: 🏗️ Создание структуры проекта и базовой конфигурации**

*   [ ] **1. Создать структуру папок и файлов:** Модульная структура поможет в будущем легко добавлять новый функционал.
    ```
    /project
    ├── app
    │   ├── __init__.py
    │   ├── core
    │   │   ├── __init__.py
    │   │   ├── config.py       # Настройки (переменные окружения)
    │   │   └── db.py           # Настройка подключения к БД и Redis
    │   ├── features            # Основная логика по задачам
    │   │   ├── __init__.py
    │   │   ├── summarization
    │   │   │   ├── __init__.py
    │   │   │   ├── api.py      # Роутер FastAPI для суммаризации
    │   │   │   ├── models.py   # Модель таблицы БД
    │   │   │   ├── schemas.py  # Модели для API (запрос/ответ)
    │   │   │   └── service.py  # Бизнес-логика
    │   │   └── subtitles
    │   │       ├── __init__.py
    │   │       ├── api.py      # Роутер FastAPI для субтитров
    │   │       ├── models.py
    │   │       ├── schemas.py
    │   │       └── service.py
    │   ├── tasks
    │   │   ├── __init__.py
    │   │   ├── summarization.py # Задача ARQ для Gemini
    │   │   └── subtitles.py     # Задача ARQ для yt-dlp
    │   ├── main.py             # Главный файл FastAPI приложения
    │   └── worker.py           # Настройки и запуск воркера ARQ
    ├── .env                    # Файл с секретами и настройками
    └── docker-compose.yml
    ```
*   [ ] **2. Настроить конфигурацию (`app/core/config.py`):**
    ```python
    # app/core/config.py
    from pydantic_settings import BaseSettings

    class Settings(BaseSettings):
        DATABASE_URL: str
        REDIS_URL: str
        GEMINI_API_KEY: str

        class Config:
            env_file = ".env"

    settings = Settings()
    ```
*   [ ] **3. Создать файл `.env`:**
    ```
    DATABASE_URL="postgresql+asyncpg://user:password@localhost/app_db"
    REDIS_URL="redis://localhost:6379"
    GEMINI_API_KEY="ВАШ_КЛЮЧ_GEMINI_API"
    ```

*   [ ] **4. Настроить подключение к БД и Redis (`app/core/db.py`):**
    ```python
    # app/core/db.py
    from sqlmodel.ext.asyncio.session import AsyncSession
    from sqlmodel import create_async_engine
    from sqlalchemy.orm import sessionmaker
    from arq import create_pool
    from arq.connections import RedisSettings

    from .config import settings

    # Настройка PostgreSQL
    engine = create_async_engine(settings.DATABASE_URL, echo=True)
    AsyncSessionFactory = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async def get_db_session() -> AsyncSession:
        async with AsyncSessionFactory() as session:
            yield session

    # Настройка Redis для ARQ
    redis_settings = RedisSettings.from_url(settings.REDIS_URL)

    async def get_redis_pool():
        return await create_pool(redis_settings)
    ```
*   [ ] **5. Настроить логирование (`app/main.py`):**
    ```python
    # В начало файла app/main.py
    import sys
    from loguru import logger

    logger.remove()
    logger.add(sys.stderr, level="INFO", format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    ```

#### **Этап 3: 📜 Задача 1 — Получение субтитров с YouTube**

*   [ ] **1. Определить модели данных (`app/features/subtitles/models.py` и `schemas.py`):**
    ```python
    # app/features/subtitles/models.py
    from sqlmodel import SQLModel, Field
    from datetime import datetime

    class SubtitlesCache(SQLModel, table=True):
        video_id: str = Field(primary_key=True)
        title: str
        original_language: str
        upload_date: datetime
        ru_subtitles: str | None
        en_subtitles: str | None
        created_at: datetime = Field(default_factory=datetime.utcnow)

    # app/features/subtitles/schemas.py
    from pydantic import BaseModel, HttpUrl

    class SubtitlesRequest(BaseModel):
        url: HttpUrl

    class SubtitlesResponse(BaseModel):
        video_id: str
        title: str
        upload_date: datetime
        original_language: str
        ru_subtitles: str | None
        en_subtitles: str | None
    ```

*   [ ] **2. Написать задачу для воркера ARQ (`app/tasks/subtitles.py`):**
    ```python
    # app/tasks/subtitles.py
    import yt_dlp
    import xml.etree.ElementTree as ET
    from datetime import datetime

    def extract_text_from_ttml(ttml_content: str) -> str:
        # ... (логика очистки от тегов)
        # Простое решение:
        try:
            root = ET.fromstring(ttml_content)
            texts = [p.text for p in root.iter() if p.text]
            return " ".join(texts).replace('\n', ' ').strip()
        except ET.ParseError:
            return ""

    async def download_subtitles(ctx, video_url: str) -> dict:
        ydl_opts = {
            'writesubtitles': True,
            'subtitleslangs': ['ru', 'en'],
            'skip_download': True,
            'writeautomaticsub': True,
            'subtitlesformat': 'ttml',
        }
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(video_url, download=False)
            video_id = info.get('id')
            
            ru_subs = next((s['ext'] == 'ttml' and s.get('data') for lang, subs in info.get('subtitles', {}).items() for s in subs if lang.startswith('ru')), None)
            en_subs = next((s['ext'] == 'ttml' and s.get('data') for lang, subs in info.get('subtitles', {}).items() for s in subs if lang.startswith('en')), None)
            
            # ... (аналогично для автоматических субтитров 'automatic_captions')

            upload_dt = datetime.strptime(info['upload_date'], '%Y%m%d') if info.get('upload_date') else None

            return {
                "video_id": video_id,
                "title": info.get('title'),
                "original_language": info.get('language'),
                "upload_date": upload_dt,
                "ru_subtitles": extract_text_from_ttml(ru_subs) if ru_subs else None,
                "en_subtitles": extract_text_from_ttml(en_subs) if en_subs else None,
            }

    ```
*   [ ] **3. Создать API-эндпоинт (`app/features/subtitles/api.py`):**
    ```python
    # app/features/subtitles/api.py
    from fastapi import APIRouter, Depends, BackgroundTasks
    from sqlmodel.ext.asyncio.session import AsyncSession
    from arq.connections import ArqRedis
    
    from app.core.db import get_db_session, get_redis_pool
    from . import schemas, service

    router = APIRouter(prefix="/subtitles", tags=["Subtitles"])

    @router.post("/", response_model=schemas.SubtitlesResponse)
    async def get_subtitles(
        request: schemas.SubtitlesRequest,
        db_session: AsyncSession = Depends(get_db_session),
        redis: ArqRedis = Depends(get_redis_pool)
    ):
        video_id = service.extract_video_id(str(request.url))
        
        # 1. Проверить кеш в БД
        cached_result = await service.get_subtitles_from_db(db_session, video_id)
        if cached_result:
            return cached_result
        
        # 2. Если в кеше нет, запустить задачу в ARQ
        job = await redis.enqueue_job('download_subtitles', str(request.url))
        
        # 3. Дождаться результата
        result_data = await job.result()

        # 4. Сохранить результат в БД в фоновом режиме
        # (или можно сделать это внутри самой задачи воркера)
        new_cache_entry = await service.save_subtitles_to_db(db_session, result_data)
        
        return new_cache_entry
    ```
    *(Примечание: логика `service.py` вынесена для чистоты, она будет содержать функции `get_subtitles_from_db`, `save_subtitles_to_db` и `extract_video_id`)*

#### **Этап 4: 🧠 Задача 2 — Суммаризация текста с Gemini**

*   [ ] **1. Определить модели данных (`app/features/summarization/...`):**
    ```python
    # app/features/summarization/models.py
    from sqlmodel import SQLModel, Field
    
    class SummarizationCache(SQLModel, table=True):
        text_hash: str = Field(primary_key=True)
        original_text: str
        summary: str
        mode: str
    
    # app/features/summarization/schemas.py
    from pydantic import BaseModel

    class SummarizationRequest(BaseModel):
        text: str
        mode: str # Например, 'short_summary', 'key_points'

    class SummarizationResponse(BaseModel):
        summary: str
    ```
*   [ ] **2. Написать задачу для воркера ARQ (`app/tasks/summarization.py`):**
    ```python
    # app/tasks/summarization.py
    import google.generativeai as genai
    from app.core.config import settings

    genai.configure(api_key=settings.GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-pro')

    # На сервере можно определить шаблоны промптов
    PROMPTS = {
        "short_summary": "Сделай краткую суммаризацию следующего текста: {text}",
        "key_points": "Выдели ключевые тезисы из следующего текста: {text}",
    }

    async def summarize_text(ctx, text: str, mode: str) -> str:
        prompt = PROMPTS.get(mode)
        if not prompt:
            raise ValueError("Invalid summarization mode")
            
        response = await model.generate_content_async(prompt.format(text=text))
        return response.text
    ```
*   [ ] **3. Создать API-эндпоинт (`app/features/summarization/api.py`):**
    *   *Действовать аналогично эндпоинту для субтитров: создать `api.py` и `service.py`. В сервисе будет функция для генерации хэша (`hashlib.sha256(text.encode()).hexdigest()`), функции для проверки и сохранения в БД. В `api.py` будет роутер, который проверяет кеш по хэшу и, если его нет, ставит задачу в очередь ARQ.*

#### **Этап 5: 🧩 Сборка и запуск приложения**

*   [ ] **1. Собрать роутеры в основном приложении (`app/main.py`):**
    ```python
    # app/main.py
    from fastapi import FastAPI
    from app.features.subtitles import api as subtitles_api
    from app.features.summarization import api as summarization_api
    
    app = FastAPI(title="Async API Server")

    app.include_router(subtitles_api.router)
    app.include_router(summarization_api.router)

    @app.get("/")
    async def root():
        return {"message": "Server is running"}
    ```
*   [ ] **2. Настроить воркер ARQ (`app/worker.py`):**
    ```python
    # app/worker.py
    from app.core.db import redis_settings
    from app.tasks.subtitles import download_subtitles
    from app.tasks.summarization import summarize_text

    class WorkerSettings:
        functions = [download_subtitles, summarize_text]
        redis_settings = redis_settings
        # Увеличьте количество одновременных задач для высокой нагрузки
        max_jobs = 50 
    ```
*   [ ] **3. Добавить команду для создания таблиц в БД:** Для этого можно использовать `alembic` или простой скрипт. Для простоты, создадим скрипт:
    ```python
    # create_db.py (в корне проекта)
    import asyncio
    from app.core.db import engine
    from app.features.subtitles.models import SubtitlesCache
    from app.features.summarization.models import SummarizationCache
    from sqlmodel import SQLModel

    async def create_tables():
        async with engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.create_all)

    if __name__ == "__main__":
        asyncio.run(create_tables())
    ```
    Выполнить один раз: `python create_db.py`
*   [ ] **4. Запустить все компоненты:**
    *   **Терминал 1 (Сервер API):** `uvicorn app.main:app --reload`
    *   **Терминал 2 (Воркер ARQ):** `arq app.worker.WorkerSettings`
    *   **Docker:** Убедитесь, что PostgreSQL и Redis запущены (`docker-compose up`).

#### **Этап 6 (Бонус): 👨‍💻 Создание клиента**

*   [ ] **1. Установить библиотеки для клиента:**
    ```bash
    pip install httpx typer rich
    ```
*   [ ] **2. Написать простой скрипт клиента (`client.py`):**
    ```python
    # client.py
    import typer
    import httpx
    from rich.console import Console
    from rich.panel import Panel

    app = typer.Typer()
    console = Console()
    BASE_URL = "http://127.0.0.1:8000"

    @app.command()
    def get_subs(url: str):
        """Запросить субтитры для YouTube видео."""
        console.print(f"Запрашиваю субтитры для: {url}...")
        with httpx.Client() as client:
            response = client.post(f"{BASE_URL}/subtitles/", json={"url": url}, timeout=120)
            if response.status_code == 200:
                console.print(Panel(str(response.json()), title="[green]Результат[/green]", expand=False))
            else:
                console.print(f"[red]Ошибка: {response.status_code}[/red]", response.text)

    # ... (аналогичная команда для summarize) ...

    if __name__ == "__main__":
        app()
    ```
*   [ ] **3. Использовать клиент:**
    ```bash
    uv run client/client.py -- get-subs "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    ```

С этим планом даже junior-разработчик сможет пошагово создать мощное, современное и надежное приложение, максимально полагаясь на готовые, хорошо документированные инструменты.