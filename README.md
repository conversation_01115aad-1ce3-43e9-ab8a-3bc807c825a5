# YT Subs & Summarization API

![Python Version](https://img.shields.io/badge/Python-3.12%2B-blue)
![FastAPI](https://img.shields.io/badge/FastAPI-0.115%2B-009688)
![ARQ](https://img.shields.io/badge/ARQ-0.26%2B-orange)
![SQLModel](https://img.shields.io/badge/SQLModel-0.0.24%2B-purple)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-lightgrey)
![Redis](https://img.shields.io/badge/Redis-red)
![yt--dlp](https://img.shields.io/badge/yt--dlp-green)
![Google Gemini API](https://img.shields.io/badge/Google_Gemini-API-blueviolet)

## 📋 Обзор проекта

**YT Subs & Summarization API** — это высокопроизводительный асинхронный веб-сервис на <PERSON>, предназначенный для:

1.  **Извлечения субтитров с YouTube-видео** с помощью `yt-dlp`
2.  **Суммаризации текстового контента** с использованием Google Gemini API

Проект построен с акцентом на скорость, надежность и масштабируемость, используя современный асинхронный стек технологий.

## 🌟 Основные возможности

### 1. Извлечение субтитров YouTube

- **Endpoint**: `POST /subtitles/`
- Быстрое получение субтитров для любого YouTube-видео
- Поддержка автоматических и ручных субтитров
- Интеллектуальное кэширование для мгновенного доступа
- Очистка и парсинг TTML/JSON3 форматов

### 2. Суммаризация текста

- **Endpoint**: `POST /summarize/`
- Глубокая суммаризация с помощью Google Gemini API
- Поддержка различных режимов суммаризации
- Кэширование результатов с хэшированием текста
- Поддержка больших текстов с разбиением на части

### 3. Управление задачами

- **Endpoints**: `/tasks/{task_id}/status`, `/tasks/{task_id}/result`
- Real-time отслеживание статуса через Server-Sent Events (SSE)
- Асинхронная обработка в фоновых воркерах
- Надежная обработка ошибок и повторные попытки

## 🏗️ Архитектура

### Основные технологии:

- **FastAPI** — сверхбыстрый веб-фреймворк для создания API
- **ARQ (Asynchronous Redis Queue)** — асинхронная очередь задач
- **SQLModel** — объединение Pydantic и SQLAlchemy для работы с данными
- **PostgreSQL** — основная база данных
- **Redis** — брокер сообщений для очереди задач
- **Google Gemini API** — для суммаризации текста
- **yt-dlp** — для извлечения субтитров YouTube
- **Loguru** — структурированное логирование

### Принципы архитектуры:

1. **Полная асинхронность** — все операции используют `async`/`await`
2. **Очередь задач** — тяжелые операции выполняются в фоновых воркерах ARQ
3. **Интеллектуальное кэширование** — результаты сохраняются в PostgreSQL
4. **Единый источник правды** — SQLModel для моделей БД, валидации и API схем

### Диаграмма архитектуры:

```mermaid
graph TD;
    User["Пользователь/Клиент"] --> FastAPI["FastAPI API"];

    subgraph API Gateway
        FastAPI -- "Кэш-проверка" --> PostgreSQL["PostgreSQL (Кэш)"];
        PostgreSQL -- "Кэш-попадание" --> FastAPI;
        FastAPI -- "Кэш-промах / Тяжелая задача" --> ARQ_Queue["ARQ (Очередь задач)"];
    end

    ARQ_Queue -- "Отправка задачи" --> Redis["Redis (Брокер сообщений)"];
    Redis -- "Забор задачи" --> ARQ_Worker["ARQ Воркеры"];

    subgraph Фоновая обработка
        ARQ_Worker -- "Запрос субтитров" --> YTDLP["yt-dlp"];
        ARQ_Worker -- "Запрос суммаризации" --> GoogleGemini["Google Gemini API"];
        ARQ_Worker -- "Сохранение результатов" --> PostgreSQL;
    end

    YTDLP --> ARQ_Worker;
    GoogleGemini --> ARQ_Worker;
    PostgreSQL -- "Данные/Результаты" --> ARQ_Worker;
    ARQ_Worker -- "Результат задачи" --> PostgreSQL;
    PostgreSQL -- "Получение результата" --> FastAPI;
    FastAPI --> User;

    %% Описание связей
    FastAPI -- "Ответ" --> User;
    ARQ_Queue -- "Управление задачами" --> FastAPI;
    ARQ_Worker -- "Логирование" --> Loguru["Loguru"];
    FastAPI -- "Логирование" --> Loguru;
```

## 🚀 Установка и запуск

### Предварительные требования:

- Python 3.12+
- Docker & Docker Compose
- `uv` (современный менеджер пакетов Python)

### Быстрый старт:

1.  **Клонирование репозитория:**
```bash
git clone https://github.com/user/yt_subs_api18.git # Замените на фактический URL вашего репозитория
cd yt_subs_api18
```

2.  **Настройка переменных окружения (`.env`):**
    Создайте файл `.env` в корневой директории проекта. Этот файл будет хранить чувствительные данные, такие как ключи API и URL баз данных. Пример `.env`:

```dotenv
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/ytsubsdb"
REDIS_URL="redis://localhost:6379"
GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
# Опционально: если вы используете прокси для доступа к Gemini API
# SOCKS5_PROXY_GEMINI="socks5://user:<EMAIL>:1080"
# Опционально: если вы используете прокси для yt-dlp (для скачивания субтитров)
# SOCKS5_PROXY_YOUTUBE="socks5://user:<EMAIL>:1080"
# Опционально: путь к файлу с куками для yt-dlp (если YouTube блокирует доступ)
# YOUTUBE_COOKIES_FILE="/path/to/cookies.txt"
# Опционально: формат субтитров, которые yt-dlp будет пытаться загрузить первым (json3 или ttml)
# SUBTITLE_FORMAT="ttml"
```

    > **Важно:** Замените значения `YOUR_GEMINI_API_KEY`, `user`, `password` и `ytsubsdb` на свои. Если вы не используете прокси, закомментируйте соответствующие строки. Ключ API Gemini можно получить на [Google AI Studio](https://aistudio.google.com/).

3.  **Запуск Docker-контейнеров (PostgreSQL и Redis):**
    Используйте `docker-compose` для быстрого развертывания необходимых сервисов:
```bash
docker-compose up -d
```
    Это запустит PostgreSQL на порту `5432` и Redis на порту `6379`.

4.  **Создание и применение миграций базы данных:**
    Проект использует Alembic для управления миграциями БД. Убедитесь, что база данных создана и примените миграции:
```bash
uv run scripts/create_db.py # Создаст базу данных, если ее нет (используя env variables)
alembic upgrade head
```
    > Если у вас возникли проблемы с Alembic, убедитесь, что `alembic.ini` настроен правильно, и переменные окружения доступны.

5.  **Установка зависимостей проекта:**
```bash
uv sync
```
`uv` автоматически установит все зависимости, указанные в `pyproject.toml`.

### Запуск сервера и воркеров

Проект поставляется с удобным раннером, который запускает как FastAPI сервер, так и ARQ воркеры. Вам не нужно запускать их по отдельности.

```bash
uv run runners/run_app_runner.py
```

Или, используя определенный скрипт из `pyproject.toml`:

```bash
uv run start_server
```

Вы увидите логи от FastAPI сервера и ARQ воркеров. API будет доступен по адресу `http://localhost:8001` (или по порту, указанному в `.env` или через `--port`).

Для остановки всех компонентов нажмите `Ctrl+C` в терминале, где запущен раннер.

## ⚡ Использование API

API доступен по адресу `http://localhost:8001`. Документация OpenAPI (Swagger UI) доступна по адресу `http://localhost:8001/docs`.

### 🎥 Получение субтитров YouTube

**Endpoint:** `POST /subtitles/`

**Описание:** Извлекает субтитры для заданного URL YouTube-видео. Если субтитры уже есть в кэше, они будут возвращены мгновенно. В противном случае, задача будет поставлена в очередь для фоновой обработки.

**Пример запроса (с использованием `curl`):**

```bash
curl -X POST "http://localhost:8001/subtitles/" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}'
```

**Пример ответа (задача в обработке):**

```json
{
  "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status_url": "http://localhost:8001/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef/status",
  "sse_url": "http://localhost:8001/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef/sse"
}
```

**Пример ответа (результат из кэша):**

```json
{
  "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "COMPLETED",
  "result_url": "http://localhost:8001/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef/result",
  "sse_url": "http://localhost:8001/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef/sse"
}
```

Вы можете отслеживать статус задачи по `status_url` или получить результат по `result_url` после завершения. Для асинхронного получения статуса и результатов рекомендуется использовать `sse_url` (Server-Sent Events).

### 📝 Суммаризация текста

**Endpoint:** `POST /summarize/`

**Описание:** Генерирует суммаризацию для предоставленного текста. Поддерживает различные режимы суммаризации (указываемые параметром `mode`). Результаты кэшируются.

**Пример запроса (с использованием `curl`):**

```bash
curl -X POST "http://localhost:8001/summarize/" \
     -H "Content-Type: application/json" \
     -d '{"text": "Your long text here...", "mode": "default"}'
```

**Пример ответа (задача в обработке):**

```json
{
  "task_id": "another-uuid-for-summarization",
  "status_url": "http://localhost:8001/tasks/another-uuid-for-summarization/status",
  "sse_url": "http://localhost:8001/tasks/another-uuid-for-summarization/sse"
}
```

**Пример ответа (результат из кэша):**

```json
{
  "task_id": "another-uuid-for-summarization",
  "status": "COMPLETED",
  "result_url": "http://localhost:8001/tasks/another-uuid-for-summarization/result",
  "sse_url": "http://localhost:8001/tasks/another-uuid-for-summarization/sse"
}
```

### 📊 Отслеживание статуса и результатов задач

**Endpoint:** `GET /tasks/{task_id}/status`

**Описание:** Получение текущего статуса задачи по её ID.

**Пример:** `http://localhost:8001/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef/status`

**Endpoint:** `GET /tasks/{task_id}/result`

**Описание:** Получение полного результата задачи по её ID (доступно только после завершения задачи).

**Пример:** `http://localhost:8001/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef/result`

**Endpoint:** `GET /tasks/{task_id}/sse`

**Описание:** Server-Sent Events (SSE) для получения обновлений статуса и результата задачи в реальном времени.

## 💻 CLI клиенты

Проект включает удобные консольные утилиты для работы с API суммаризации:

```bash
# Основной клиент
uv run summaries --directory ./texts --mode default --api-key YOUR_KEY

# Клиент с улучшенным UI
uv run summaries_alt --directory ./texts --mode default --api-key YOUR_KEY
```

## 🔧 Конфигурация

Основные переменные окружения:

- `DATABASE_URL` — URL подключения к PostgreSQL
- `REDIS_URL` — URL подключения к Redis
- `GEMINI_API_KEY` — ключ API Google Gemini
- `SOCKS5_PROXY_GEMINI` — прокси для Gemini API (опционально)
- `SOCKS5_PROXY_YOUTUBE` — прокси для YouTube (опционально)
- `YOUTUBE_COOKIES_FILE` — файл с куками для yt-dlp (опционально)

## 🎯 Особенности реализации

1.  **Полная асинхронность** — все I/O операции неблокирующие
2.  **Graceful degradation** — система продолжает работать при недоступности внешних сервисов
3.  **Rate limiting** — защита от перегрузки через SlowAPI
4.  **Comprehensive logging** — детальное логирование всех операций
5.  **Health checks** — проверка доступности внешних ресурсов при старте
6.  **Модульная архитектура** — легкое добавление новых функций

## 📊 Технические характеристики

### Производительность:

- Асинхронная обработка тысяч запросов в секунду
- Интеллектуальное кэширование снижает нагрузку на внешние API
- Пул соединений с базой данных для оптимизации
- Rate limiting для защиты от перегрузки

### Надежность:

- Graceful handling всех внешних зависимостей
- Автоматические повторные попытки для неустойчивых операций
- Comprehensive error handling и логирование
- Health checks при запуске компонентов

### Масштабируемость:

- Горизонтальное масштабирование воркеров ARQ
- Stateless API сервер
- Кэширование в PostgreSQL
- Поддержка прокси для работы в различных сетевых условиях

## 🤝 Вклад в проект

Мы приветствуем любые вклады в развитие проекта! Если у вас есть идеи, предложения или вы нашли ошибку, пожалуйста:

1.  Откройте [Issue](https://github.com/user/yt_subs_api18/issues) с подробным описанием.
2.  Создайте [Pull Request](https://github.com/user/yt_subs_api18/pulls) с вашими изменениями.

Пожалуйста, убедитесь, что ваш код соответствует нашим архитектурным принципам и стилю.

## 📜 Лицензия

Этот проект распространяется под лицензией MIT. Подробности смотрите в файле `LICENSE` (если он есть, или добавьте его).
