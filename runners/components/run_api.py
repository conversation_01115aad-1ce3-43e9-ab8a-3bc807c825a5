import uvicorn
import argparse
from app.core.logging_config import configure_logging

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run FastAPI application.")
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port for FastAPI server (default: 8000).",
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reloading of the application on code changes.",
    )
    parser.add_argument(
        "--ssl-keyfile",
        type=str,
        default=None,
        help="Path to the SSL key file.",
    )
    parser.add_argument(
        "--ssl-certfile",
        type=str,
        default=None,
        help="Path to the SSL certificate file.",
    )
    args = parser.parse_args()

    configure_logging("API")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=args.port,
        reload=args.reload,
        ssl_keyfile=args.ssl_keyfile,
        ssl_certfile=args.ssl_certfile,
        log_config=None,  # Disable uvicorn's default logging configuration
    )
