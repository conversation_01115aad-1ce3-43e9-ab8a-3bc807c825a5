import sys
from arq.worker import run_worker
from redis.exceptions import ConnectionError, RedisError  # Импортируем ошибки Redis
from loguru import logger

from app.worker import WorkerSettings
from app.core.logging_config import configure_logging

if __name__ == "__main__":
    # Настраиваем логирование для этого процесса
    configure_logging("WORKER")

    try:
        logger.info("Запуск воркера ARQ...")
        # Основной блокирующий вызов, который запускает цикл воркера
        run_worker(WorkerSettings)
        # Эта строка выполнится только если воркер завершится штатно (например, по сигналу)
        logger.info("Воркер ARQ завершил работу штатно.")

    except (ConnectionError, RedisError) as e:
        # Ловим КОНКРЕТНЫЕ ошибки соединения с Redis
        # Используем logger.critical для самых важных ошибок
        logger.critical(
            "КРИТИЧЕСКАЯ ОШИБКА: Воркер не может подключиться к Redis. "
            f"Проверьте доступность Redis и сетевые настройки. Ошибка: {e}"
        )
        # Завершаем процесс с кодом ошибки, чтобы показать, что запуск провалился
        sys.exit(1)

    except Exception:
        # Ловим любые другие непредвиденные ошибки на старте
        # logger.exception добавит трейсбек в лог, но уже в красивом формате loguru
        logger.exception(
            "КРИТИЧЕСКАЯ ОШИБКА: Непредвиденная ошибка при запуске воркера."
        )
        sys.exit(1)
