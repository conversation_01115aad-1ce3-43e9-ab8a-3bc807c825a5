import argparse
import subprocess
import os
import signal
import sys
import time
import socket
from dotenv import load_dotenv
from urllib.parse import urlparse
from loguru import logger
from app.core.logging_config import configure_logging
from pathlib import Path

# Configure Loguru
configure_logging("MANAGER")

# Fixed default values for command line arguments
DEFAULT_FASTAPI_PORT = 8001
DEFAULT_ARQ_WORKERS = 1
DEFAULT_FASTAPI_RELOAD = False  # Default to False, will be true if --reload is passed

# List to hold subprocesses and their command for better logging
running_processes = []


def cleanup(signum, frame):
    """Gracefully shuts down all running subprocesses."""
    logger.info(
        f"Получен сигнал {signum if signum else 'N/A'}. Инициируется плавное завершение..."
    )
    for proc_info in running_processes:
        proc = proc_info["process"]
        if proc.poll() is None:  # Only attempt to terminate if still running
            logger.info(
                f"Завершение процесса: {' '.join(proc_info['command'])} (PID: {proc.pid})"
            )
            try:
                proc.terminate()
                proc.wait(timeout=5)  # Give it 5 seconds to terminate gracefully
            except subprocess.TimeoutExpired:
                logger.warning(
                    f"Процесс {' '.join(proc_info['command'])} (PID: {proc.pid}) не завершился корректно. Принудительное завершение."
                )
                proc.kill()
            except Exception as e:
                logger.error(
                    f"Ошибка при завершении процесса {' '.join(proc_info['command'])} (PID: {proc.pid}): {e}"
                )

    logger.info("Все компоненты приложения завершены.")
    sys.exit(0)


# Register signal handlers for graceful exit
signal.signal(signal.SIGINT, cleanup)
signal.signal(signal.SIGTERM, cleanup)


def check_port_in_use(host, port, timeout=1):
    """
    Checks if a given port on a host is in use.
    Returns True if in use, False otherwise.
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(timeout)
        try:
            s.connect((host, int(port)))
            return True
        except (socket.timeout, ConnectionRefusedError, OSError):
            return False


def main():
    parser = argparse.ArgumentParser(description="Manage FastAPI and ARQ workers.")
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_FASTAPI_PORT,
        help="Port for FastAPI server (default: 8000).",
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=DEFAULT_ARQ_WORKERS,
        help="Number of ARQ workers to run (default: 1).",
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Reload FastAPI on code changes.",
    )
    parser.add_argument(
        "--ssl-keyfile",
        type=str,
        # default="keys/localhost+2-key.pem",
        help="Path to the SSL key file for FastAPI.",
    )
    parser.add_argument(
        "--ssl-certfile",
        type=str,
        # default="keys/localhost+2.pem",
        help="Path to the SSL certificate file for FastAPI.",
    )
    args = parser.parse_args()

    fastapi_port = args.port
    num_arq_workers = args.workers

    # Load environment variables from .env file
    logger.info("Загрузка переменных окружения из .env...")
    if not load_dotenv():
        logger.error(
            "Ошибка: Файл .env не найден. Пожалуйста, создайте его с переменной REDIS_URL."
        )
        sys.exit(1)

    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        logger.error("Ошибка: REDIS_URL не найден в .env. Пожалуйста, укажите его.")
        sys.exit(1)

    # Extract Redis host and port from REDIS_URL
    try:
        parsed_redis_url = urlparse(redis_url)
        redis_host = parsed_redis_url.hostname
        redis_port = parsed_redis_url.port
        if not redis_host or not redis_port:
            raise ValueError("Invalid REDIS_URL format.")
    except Exception as e:
        logger.error(
            f"Ошибка разбора REDIS_URL '{redis_url}': {e}. Убедитесь, что формат соответствует redis://хост:порт"
        )
        sys.exit(1)

    # Check Redis availability
    logger.info(
        f"Проверка доступности Redis на {redis_host}:{redis_port} (таймаут 3с)..."
    )
    if not check_port_in_use(redis_host, redis_port, timeout=3):  # 3s timeout for Redis
        logger.error(f"Ошибка: Redis недоступен по адресу {redis_host}:{redis_port}.")
        logger.error(
            "Пожалуйста, убедитесь, что Redis запущен (например, через 'docker-compose up -d')."
        )
        sys.exit(1)
    logger.success("Redis доступен.")

    # Check if FastAPI port is already in use
    logger.info(f"Проверка доступности порта {fastapi_port}...")
    if check_port_in_use("localhost", fastapi_port):
        logger.error(f"Ошибка: Порт {fastapi_port} уже используется.")
        logger.error(
            "Освободите порт или выберите другой с помощью --port <НОВЫЙ_ПОРТ>."
        )
        sys.exit(1)
    logger.info(f"Порт {fastapi_port} доступен.")

    # Get the path to the current directory (runners/components)
    current_dir = Path(__file__).resolve().parent

    # Construct full paths to run_api.py and run_worker.py
    run_api_path = current_dir / "run_api.py"
    run_worker_path = current_dir / "run_worker.py"

    # Launch FastAPI server
    logger.info(f"Запуск FastAPI сервера на порту {fastapi_port}...")
    fastapi_cmd = [
        "uv",
        "run",
        str(run_api_path),
        "--port",
        str(fastapi_port),
    ]
    if args.reload or DEFAULT_FASTAPI_RELOAD:
        fastapi_cmd.append("--reload")
    if args.ssl_keyfile:
        fastapi_cmd.extend(["--ssl-keyfile", args.ssl_keyfile])
    if args.ssl_certfile:
        fastapi_cmd.extend(["--ssl-certfile", args.ssl_certfile])
    try:
        # Let subprocesses inherit stdout/stderr so their logs are visible
        fastapi_process = subprocess.Popen(fastapi_cmd)
        running_processes.append({"process": fastapi_process, "command": fastapi_cmd})
        logger.info(f"FastAPI PID процесса: {fastapi_process.pid}")
    except FileNotFoundError:
        logger.error(
            "Ошибка: команда 'uv' не найдена. Убедитесь, что uv добавлен в PATH."
        )
        cleanup(None, None)
        sys.exit(1)
    except Exception as e:
        logger.error(f"Ошибка при запуске FastAPI: {e}")
        cleanup(None, None)
        sys.exit(1)

    # Give FastAPI a moment to start up and potentially bind to the port
    time.sleep(2)

    # Launch ARQ workers
    logger.info(f"Запуск {num_arq_workers} воркера(ов) ARQ...")
    arq_cmd = ["uv", "run", str(run_worker_path)]
    for i in range(num_arq_workers):
        try:
            arq_process = subprocess.Popen(arq_cmd)
            running_processes.append({"process": arq_process, "command": arq_cmd})
            logger.info(f"ARQ воркер {i + 1} PID: {arq_process.pid}")
            time.sleep(0.5)  # Small delay to avoid race conditions/overload on startup
        except FileNotFoundError:
            logger.error(
                "Ошибка: команда 'uv' не найдена. Убедитесь, что uv добавлен в PATH."
            )
            cleanup(None, None)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Ошибка при запуске ARQ воркера {i + 1}: {e}")
            cleanup(None, None)
            sys.exit(1)

    logger.info("Компоненты приложения запущены. Нажмите Ctrl+C для остановки.")

    # Keep the main script alive while subprocesses run
    # This loop will continue until a signal is received
    try:
        while True:
            # In a more advanced manager, you might check proc.poll() here
            # to detect unexpected process termination and handle it (e.g., restart)
            time.sleep(1)
    except KeyboardInterrupt:
        # This block is for direct Ctrl+C; signal handler will also catch it
        logger.info(
            "Обнаружен KeyboardInterrupt в основном цикле. Обработчик сигнала выполнит завершение."
        )
        # The signal handler will be called immediately after this.


if __name__ == "__main__":
    main()
