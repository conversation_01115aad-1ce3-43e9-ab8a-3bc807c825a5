import asyncio
import httpx
import time
import argparse
from loguru import logger
from typing import Optional

# Configure <PERSON><PERSON><PERSON> for better output
logger.remove()
logger.add(
    "stress_test.log",
    rotation="10 MB",
    level="INFO",
    format="{time} {level} {message}",
    colorize=False,
)
logger.add(
    "stderr.log",
    level="INFO",
    format="<green>{time:HH:mm:ss}</green> <level>{level}</level> <level>{message}</level>",
)


async def send_summarize_request(
    client: httpx.AsyncClient,
    base_url: str,
    text_content: str,
    mode: str,
    api_key: Optional[str] = None,
    request_id: int = 0,
) -> dict:
    """Sends a single summarization request and returns metrics."""
    url = f"{base_url}/summarize/"
    payload = {"text": text_content, "mode": mode}
    headers = {}
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"

    start_time = time.monotonic()
    try:
        response = await client.post(url, json=payload, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
        end_time = time.monotonic()
        duration = end_time - start_time
        logger.info(
            f"Request {request_id}: SUCCESS - Status {response.status_code}, Duration {duration:.4f}s"
        )
        return {"status": "success", "duration": duration, "response": response.json()}
    except httpx.ConnectError as e:
        end_time = time.monotonic()
        duration = end_time - start_time
        logger.error(
            f"Request {request_id}: CONNECT ERROR - Server unavailable: {e}, Duration {duration:.4f}s"
        )
        return {"status": "connect_error", "duration": duration, "error": str(e)}
    except httpx.TimeoutException as e:
        end_time = time.monotonic()
        duration = end_time - start_time
        logger.error(
            f"Request {request_id}: TIMEOUT ERROR - {e}, Duration {duration:.4f}s"
        )
        return {"status": "timeout_error", "duration": duration, "error": str(e)}
    except httpx.HTTPStatusError as e:
        end_time = time.monotonic()
        duration = end_time - start_time
        logger.error(
            f"Request {request_id}: HTTP ERROR - Status {e.response.status_code}, Detail: {e.response.text}, Duration {duration:.4f}s"
        )
        return {
            "status": "http_error",
            "duration": duration,
            "error": str(e),
            "status_code": e.response.status_code,
            "detail": e.response.text,
        }
    except Exception as e:
        end_time = time.monotonic()
        duration = end_time - start_time
        logger.error(
            f"Request {request_id}: UNEXPECTED ERROR - {e}, Duration {duration:.4f}s",
            exc_info=True,
        )
        return {"status": "unexpected_error", "duration": duration, "error": str(e)}


async def main():
    parser = argparse.ArgumentParser(
        description="Stress test for the /summarize endpoint."
    )
    parser.add_argument("--host", type=str, default="localhost", help="API server host")
    parser.add_argument("--port", type=int, default=8001, help="API server port")
    parser.add_argument(
        "--api-key", type=str, default=None, help="Optional API Key for authentication"
    )
    parser.add_argument(
        "--file",
        type=str,
        required=True,
        help="Path to the text file for summarization",
    )
    parser.add_argument(
        "--mode",
        type=str,
        default="default",
        help="Summarization mode (e.g., 'default')",
    )
    parser.add_argument(
        "--concurrent-users",
        type=int,
        default=5,
        help="Number of concurrent requests (simulated users)",
    )
    parser.add_argument(
        "--total-requests",
        type=int,
        default=50,
        help="Total number of requests to send",
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=120,
        help="Timeout for each HTTP request in seconds",
    )

    args = parser.parse_args()

    base_url = f"http://{args.host}:{args.port}"
    logger.info(f"Starting stress test for {base_url}/summarize/")
    logger.info(f"File: {args.file}, Mode: {args.mode}")
    logger.info(
        f"Concurrent Users: {args.concurrent_users}, Total Requests: {args.total_requests}"
    )
    logger.info(f"Request Timeout: {args.timeout}s")

    try:
        with open(args.file, "r", encoding="utf-8") as f:
            text_content = f.read()
        logger.info(
            f"Successfully read file: {args.file} (size: {len(text_content)} bytes)"
        )
    except FileNotFoundError:
        logger.error(f"Error: File not found at {args.file}")
        return
    except Exception as e:
        logger.error(f"Error reading file {args.file}: {e}")
        return

    results = []
    sem = asyncio.Semaphore(args.concurrent_users)
    request_counter = 0

    async def worker():
        nonlocal request_counter
        while True:
            await sem.acquire()
            if request_counter >= args.total_requests:
                sem.release()
                break
            current_request_id = request_counter
            request_counter += 1
            logger.debug(f"Initiating request {current_request_id}...")
            try:
                # Using a single httpx.AsyncClient for connection pooling
                async with httpx.AsyncClient(timeout=args.timeout) as client:
                    result = await send_summarize_request(
                        client,
                        base_url,
                        text_content,
                        args.mode,
                        args.api_key,
                        current_request_id,
                    )
                results.append(result)
            finally:
                sem.release()

    start_test_time = time.monotonic()
    tasks = [asyncio.create_task(worker()) for _ in range(args.concurrent_users)]
    # Wait for all requests to be initiated and processed
    # This might need adjustment if total_requests is very high and workers finish early
    while request_counter < args.total_requests:
        await asyncio.sleep(0.1)  # Give workers a chance to pick up tasks

    # Wait for all pending tasks to complete
    await asyncio.gather(*tasks)

    end_test_time = time.monotonic()
    total_test_duration = end_test_time - start_test_time

    # Collect and report statistics
    successful_requests = [r for r in results if r["status"] == "success"]
    failed_requests = [r for r in results if r["status"] != "success"]
    total_requests_completed = len(results)

    total_successful_duration = sum(r["duration"] for r in successful_requests)
    average_successful_duration = (
        total_successful_duration / len(successful_requests)
        if successful_requests
        else 0
    )

    logger.info("\n--- Stress Test Results ---")
    logger.info(f"Total Test Duration: {total_test_duration:.2f} seconds")
    logger.info(f"Total Requests Attempted: {args.total_requests}")
    logger.info(f"Total Requests Completed (Processed): {total_requests_completed}")
    logger.info(f"Successful Requests: {len(successful_requests)}")
    logger.info(f"Failed Requests: {len(failed_requests)}")

    if total_requests_completed > 0:
        throughput = total_requests_completed / total_test_duration
        logger.info(f"Throughput: {throughput:.2f} requests/second")
    else:
        logger.info("No requests completed.")

    if successful_requests:
        logger.info(
            f"Average Successful Request Duration: {average_successful_duration:.4f} seconds"
        )
        min_duration = min(r["duration"] for r in successful_requests)
        max_duration = max(r["duration"] for r in successful_requests)
        logger.info(f"Min Successful Request Duration: {min_duration:.4f} seconds")
        logger.info(f"Max Successful Request Duration: {max_duration:.4f} seconds")
    else:
        logger.info("No successful requests to report duration statistics.")

    if failed_requests:
        logger.warning("\n--- Details of Failed Requests ---")
        for i, failure in enumerate(failed_requests):
            logger.warning(
                f"Failure {i + 1}: Status: {failure['status']}, Duration: {failure['duration']:.4f}s, Error: {failure.get('error')}"
            )
            if "status_code" in failure:
                logger.warning(
                    f"  HTTP Status: {failure['status_code']}, Detail: {failure.get('detail')}"
                )


if __name__ == "__main__":
    asyncio.run(main())

# uv run stress_test_summarize.py --file sample_text.txt --host localhost --port 8001 --concurrent-users 10 --total-requests 100 --mode short#1
