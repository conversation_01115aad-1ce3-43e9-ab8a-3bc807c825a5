import asyncio
import sys
from pathlib import Path

from app.core.db import engine
from sqlmodel import SQLModel

# Add a logger for better feedback
from loguru import logger

# Add project root to sys.path for module imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logger.add(sys.stderr, format="{time} {level} {message}", level="INFO")


async def create_tables():
    async with engine.begin() as conn:
        # logger.info("Attempting to drop 'summarizationcache' table if it exists...")
        # await conn.execute(text("DROP TABLE IF EXISTS summarizationcache CASCADE;"))
        logger.info(
            "'summarizationcache' table dropped if it existed. Creating all tables..."
        )

        await conn.run_sync(SQLModel.metadata.create_all)
        logger.info("All tables created/updated successfully.")


if __name__ == "__main__":
    asyncio.run(create_tables())
