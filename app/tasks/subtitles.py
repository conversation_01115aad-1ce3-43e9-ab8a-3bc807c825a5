# app/tasks/subtitles.py

from app.tasks.subs_utils.orchestrator import SubtitleOrchestrator
from app.core.exceptions import WorkerFatalError


async def download_subtitles(ctx: dict, video_url: str, task_id: str) -> None:
    """
    ARQ-задача для загрузки субтитров.
    Создает и запускает экземпляр SubtitleOrchestrator.
    """
    try:
        orchestrator = SubtitleOrchestrator(
            video_url=video_url,
            task_id=task_id,
            db_session_factory=ctx["db_session_factory"],
        )
        await orchestrator.run(ctx["redis"])
    except WorkerFatalError as e:
        # Фатальные ошибки нужно пробрасывать выше, чтобы ARQ мог
        # корректно обработать ситуацию (например, остановить воркер).
        # Логирование уже произошло внутри оркестратора.
        raise e
