import asyncio
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Coroutine, Dict, Optional

import aiohttp
import yt_dlp
from loguru import logger

from app.tasks.subs_utils.language import LanguageManager
from app.tasks.subs_utils.parser import SubtitleParser


class BaseSubtitleExtractor(ABC):
    """Абстрактный базовый класс для стратегий извлечения субтитров."""

    def __init__(self, lang_manager: LanguageManager, original_language: str):
        self.lang_manager = lang_manager
        self.original_language = original_language

    @abstractmethod
    async def extract(
        self, ydl: yt_dlp.YoutubeDL, info: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Основной метод, который должен быть реализован в каждой стратегии.
        Возвращает словарь, где ключ - язык (ru/en), а значение - текст субтитров.
        """
        pass


class Json3Extractor(BaseSubtitleExtractor):
    """Стратегия для извлечения субтитров в формате JSON3 по URL."""

    @staticmethod
    async def _download_content(
        url: str, session: aiohttp.ClientSession
    ) -> Optional[str]:
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientError as e:
            logger.error(f"Ошибка загрузки JSON3 контента из {url}: {e}")
            return None

    def _find_best_json3_url(self, subs_list: list) -> Optional[str]:
        for sub_info in subs_list:
            if sub_info.get("ext") == "json3" and sub_info.get("url"):
                return sub_info["url"]
        return None

    async def extract(
        self, ydl: yt_dlp.YoutubeDL, info: Dict[str, Any]
    ) -> Dict[str, str]:
        logger.debug("Используется стратегия извлечения JSON3.")

        selected_subs = self.lang_manager.select_subtitles_for_download(
            info, self.original_language
        )

        tasks: Dict[str, Coroutine[Any, Any, Optional[str]]] = {}
        async with aiohttp.ClientSession() as session:
            for lang, subs_list in selected_subs.items():
                if url := self._find_best_json3_url(subs_list):
                    tasks[lang] = self._download_content(url, session)

            if not tasks:
                logger.warning("Не найдено URL для субтитров формата JSON3.")
                return {}

            results = await asyncio.gather(*tasks.values())
            downloaded_content = dict(zip(tasks.keys(), results))

        return {
            lang: SubtitleParser.parse(content)
            for lang, content in downloaded_content.items()
            if content
        }


class TtmlExtractor(BaseSubtitleExtractor):
    """Стратегия для извлечения субтитров в формате TTML через скачивание файла."""

    def __init__(
        self, lang_manager: LanguageManager, temp_dir: str, original_language: str
    ):
        super().__init__(lang_manager, original_language)
        self._temp_dir_path = Path(temp_dir)

    async def extract(
        self, ydl: yt_dlp.YoutubeDL, info: Dict[str, Any]
    ) -> Dict[str, str]:
        logger.debug(
            f"Используется стратегия извлечения TTML в директорию {self._temp_dir_path}"
        )
        video_id = info["id"]
        video_url = info["webpage_url"]

        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, lambda: ydl.download([video_url]))
        logger.debug(f"TTML-файлы для video_id={video_id} скачаны.")

        subtitles_text = {}
        for lang in self.lang_manager.TARGET_LANGS:
            expected_file = self._temp_dir_path / f"{video_id}.{lang}.ttml"
            if not expected_file.is_file():
                logger.warning(
                    f"Не найден файл TTML субтитров для языка {lang}: {expected_file}"
                )
                continue

            try:
                content = expected_file.read_text(encoding="utf-8")
                processed_text = SubtitleParser.parse(content)
                if processed_text:
                    subtitles_text[lang] = processed_text
                    logger.debug(f"Успешно обработан TTML файл {expected_file.name}")
            except Exception as e:
                logger.error(
                    f"Ошибка чтения или обработки TTML файла {expected_file.name}: {e}"
                )
        return subtitles_text
