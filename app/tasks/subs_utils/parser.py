import json
import re
import xml.etree.ElementTree as ET
from loguru import logger


class SubtitleParser:
    """Группирует статичные методы для парсинга различных форматов субтитров."""

    @staticmethod
    def _parse_json_subtitle(subtitle_json: dict) -> str:
        """Извлекает текст из структуры JSON3."""
        if not isinstance(subtitle_json, dict) or "events" not in subtitle_json:
            return ""
        texts = []
        for event in subtitle_json.get("events", []):
            if not isinstance(event, dict):
                continue
            for segment in event.get("segs", []):
                if isinstance(segment, dict) and (
                    text := segment.get("utf8", "").strip()
                ):
                    texts.append(text)
        return " ".join(texts).replace("\n", " ").strip()

    @staticmethod
    def _parse_ttml_subtitle(ttml_content: str) -> str:
        """Извлекает текст из TTML, используя ElementTree с фолбэком на regex."""
        try:
            content_bytes = (
                ttml_content.encode("utf-8")
                if isinstance(ttml_content, str)
                else ttml_content
            )
            root = ET.fromstring(content_bytes)
            ns = {"tt": "http://www.w3.org/ns/ttml"}
            texts = [
                "".join(p.itertext()).strip()
                for p in root.findall(".//tt:p", ns)
                if "".join(p.itertext()).strip()
            ]
            if texts:
                return "\n".join(texts).strip()

            logger.warning("No text found in TTML using namespace. Trying without.")
            texts = [
                elem.text.strip()
                for elem in root.iter()
                if elem.text and elem.text.strip()
            ]
            return "\n".join(texts).strip()

        except (ET.ParseError, Exception) as e:
            logger.warning(
                f"Could not parse TTML with ElementTree ({type(e).__name__}). Falling back to regex."
            )
            content_str = (
                ttml_content.decode("utf-8")
                if isinstance(ttml_content, bytes)
                else ttml_content
            )
            clean_text = re.sub(r"<[^>]+>", " ", content_str)
            return re.sub(r"\s+", " ", clean_text).strip()

    @staticmethod
    def parse(content: str) -> str:
        """
        Определяет формат и парсит контент субтитров для извлечения чистого текста.
        """
        if not content:
            return ""
        stripped_content = content.strip()
        if stripped_content.startswith("{") and stripped_content.endswith("}"):
            try:
                return SubtitleParser._parse_json_subtitle(json.loads(stripped_content))
            except json.JSONDecodeError:
                pass
        return SubtitleParser._parse_ttml_subtitle(stripped_content)
