from typing import Any, Dict, Optional
from loguru import logger

from app.core.config import settings
from app.tasks.subs_utils.language import LanguageManager


class YTDLStdoutLogger:
    """Направляет логи yt-dlp в loguru для консистентного логирования."""

    def debug(self, msg: str) -> None:
        if msg.startswith("[debug] "):
            logger.debug(f"[yt-dlp] {msg[len('[debug] ') :]}")

    def warning(self, msg: str) -> None:
        logger.warning(f"[yt-dlp] {msg}")

    def error(self, msg: str) -> None:
        logger.error(f"[yt-dlp] {msg}")


def build_ydl_options(
    sub_format: str, out_path: Optional[str] = None
) -> Dict[str, Any]:
    """Собирает конфигурацию для yt-dlp."""
    opts = {
        "quiet": True,
        "no_warnings": True,
        "skip_download": True,
        "logger": YTDLStdoutLogger(),
        "writesubtitles": True,
        "writeautomaticsub": True,
        "subtitleslangs": LanguageManager.TARGET_LANGS,
        "subtitlesformat": sub_format,
    }

    if sub_format == "ttml" and out_path:
        opts["outtmpl"] = f"{out_path}/%(id)s.%(ext)s"

    # Для получения полной информации о субтитрах (включая URL)
    # всегда запрашиваем и json3, даже если будем качать ttml.
    # yt-dlp добавит 'json3' к 'subtitlesformat', если его там нет.
    if "json3" not in opts["subtitlesformat"]:
        opts["subtitlesformat"] += "/json3"

    if settings.SOCKS5_PROXY_YOUTUBE:
        opts["proxy"] = settings.SOCKS5_PROXY_YOUTUBE
    if settings.YOUTUBE_COOKIES_FILE:
        opts["cookiefile"] = settings.YOUTUBE_COOKIES_FILE

    return opts
