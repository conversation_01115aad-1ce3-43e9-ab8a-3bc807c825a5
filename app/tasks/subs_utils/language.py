import re
from typing import Any, Dict, List, Optional
from loguru import logger


class LanguageManager:
    """
    Отвечает за определение оригинального языка видео и выбор
    наилучших доступных субтитров для целевых языков.
    """

    TARGET_LANGS = ["ru", "en"]

    @staticmethod
    def _clean_lang_code(raw_code: str) -> Optional[str]:
        """
        Очищает код языка от суффиксов.
        Примеры: 'ru-orig' -> 'ru', 'en-US' -> 'en', 'zh-Hans' -> 'zh-Hans'.
        Возвращает None для невалидных кодов вроде 'live_chat'.
        """
        if not raw_code or not isinstance(raw_code, str):
            return None

        # Отбрасываем заведомо не языковые ключи
        if "live_chat" in raw_code:
            return None

        # 'ru-orig' -> 'ru'
        if raw_code.endswith("-orig"):
            return raw_code[:-5]

        # 'ru.orig' -> 'ru'
        if raw_code.endswith(".orig"):
            return raw_code[:-5]

        # 'en-US' -> 'en', 'pt-PT' -> 'pt'
        match = re.match(r"^([a-z]{2,3})([-_][A-Z]{2,4})?$", raw_code)
        if match:
            return match.group(1)

        # Для сложных кодов, как 'zh-Hans', возвращаем как есть
        # и надеемся на прямое совпадение в TARGET_LANGS
        if "-" in raw_code:
            return raw_code

        # Простой код языка
        return raw_code if len(raw_code) == 2 else None

    def find_original_language(self, info: Dict[str, Any]) -> str:
        """
        Определяет оригинальный язык видео.
        Приоритет:
        1. Субтитры с суффиксом '-orig'.
        2. Поле 'language' в метаданных видео.
        3. По умолчанию 'en'.
        """
        all_available_subs_keys = set()
        sub_sources = [
            ("subtitles", "вручную"),
            ("automatic_captions", "автоматически"),
        ]

        for source_key, _ in sub_sources:
            for raw_code in info.get(source_key, {}).keys():
                all_available_subs_keys.add(raw_code)

        logger.debug(f"Доступные коды субтитров: {list(all_available_subs_keys)}")

        for raw_code in all_available_subs_keys:
            if raw_code.endswith("orig") or raw_code.endswith(".orig"):
                lang = self._clean_lang_code(raw_code)
                if lang:
                    logger.debug(
                        f"Оригинальный язык определен как '{lang}' по суффиксу 'orig'."
                    )
                    return lang

        if lang := info.get("language"):
            cleaned_lang = self._clean_lang_code(lang)
            if cleaned_lang in self.TARGET_LANGS:
                logger.debug(
                    f"Оригинальный язык определен как '{cleaned_lang}' из метаданных видео."
                )
                return cleaned_lang

        logger.warning(
            "Не удалось определить оригинальный язык. По умолчанию используется 'en'."
        )
        return "en"

    def select_subtitles_for_download(
        self, info: Dict[str, Any], original_language: str
    ) -> Dict[str, List[Dict]]:
        """
        Выбирает наилучшие субтитры для каждого целевого языка.
        Приоритет: созданные вручную ('subtitles') > автоматические ('automatic_captions').
        Возвращает словарь вида: {'ru': [sub_info_1, ...], 'en': [sub_info_2, ...]}
        """
        selected_subs = {lang: [] for lang in self.TARGET_LANGS}

        sub_sources = [
            ("subtitles", "вручную"),
            ("automatic_captions", "автоматически"),
        ]

        all_available = {}
        for source_key, _ in sub_sources:
            for raw_code, subs_list in info.get(source_key, {}).items():
                cleaned_code = self._clean_lang_code(raw_code)
                if cleaned_code:
                    if cleaned_code not in all_available:
                        all_available[cleaned_code] = []
                    all_available[cleaned_code].extend(subs_list)

        for lang in self.TARGET_LANGS:
            if lang in all_available:
                selected_subs[lang] = all_available[lang]
                logger.debug(f"Найдены субтитры для языка '{lang}'.")

        return selected_subs
