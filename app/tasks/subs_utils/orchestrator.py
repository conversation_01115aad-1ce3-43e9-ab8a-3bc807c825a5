import asyncio
import tempfile
from datetime import datetime
from typing import Any, Dict

import yt_dlp
from loguru import logger

from app.core.config import settings
from app.features.subtitles import service as subtitles_service
from app.core.exceptions import SubtitlesNotFoundError, WorkerFatalError
from app.tasks.subs_utils.extractors import Json3Extractor, TtmlExtractor
from app.tasks.subs_utils.language import LanguageManager
from app.tasks.subs_utils.ydl_config import build_ydl_options
from app.tasks.base_orchestrator import BaseTaskOrchestrator


class SubtitleOrchestrator(BaseTaskOrchestrator):
    """
    Класс-оркестратор, управляющий всем процессом загрузки и обработки субтитров,
    включая логику выбора стратегии и fallback.
    """

    def __init__(self, video_url: str, task_id: str, db_session_factory):
        super().__init__(task_id, db_session_factory)
        self.video_url = video_url
        self.loop = asyncio.get_running_loop()
        self.lang_manager = LanguageManager()

    async def _get_video_info(self) -> Dict[str, Any]:
        """
        Извлекает метаданные видео. Мы всегда запрашиваем json3, чтобы
        в 'info' были URL субтитров, даже если мы потом будем качать ttml.
        """
        opts = build_ydl_options("json3")
        with yt_dlp.YoutubeDL(opts) as ydl:
            try:
                info = await self.loop.run_in_executor(
                    None, lambda: ydl.extract_info(self.video_url, download=False)
                )
                if not info or not info.get("id"):
                    raise ValueError("Не удалось извлечь метаданные или ID видео.")
                logger.debug(f"Успешно извлечена информация для video_id={info['id']}")
                # logger.debug(f"Полная информация о видео (первые 500 символов): {str(info)[:500]}")
                return info
            except yt_dlp.utils.DownloadError as e:
                if "proxy" in str(e).lower() or "resolve host" in str(e).lower():
                    raise WorkerFatalError(
                        f"Неисправимая ошибка сети/прокси: {e}"
                    ) from e
                raise SubtitlesNotFoundError(
                    f"yt-dlp не смог обработать URL: {e}"
                ) from e

    async def _try_extraction_strategies(
        self, info: Dict[str, Any], original_language: str
    ) -> Dict[str, str]:
        """
        Последовательно пытается извлечь субтитры, используя доступные стратегии.
        """
        subtitles = {}

        # Создаем список стратегий в порядке приоритета
        with tempfile.TemporaryDirectory() as temp_dir:
            strategies = {
                "ttml": TtmlExtractor(self.lang_manager, temp_dir, original_language),
                "json3": Json3Extractor(self.lang_manager, original_language),
            }

            # Определяем порядок попыток
            preferred_order = [settings.SUBTITLE_FORMAT] + [
                fmt for fmt in strategies if fmt != settings.SUBTITLE_FORMAT
            ]  # Например, ['ttml', 'json3']

            for format_name in preferred_order:
                self.log.info(
                    f"Попытка извлечения субтитров в формате: {format_name.upper()}"
                )
                extractor = strategies[format_name]

                try:
                    # Для TTML нужно пересоздать ydl с правильными опциями
                    if format_name == "ttml":
                        ttml_opts = build_ydl_options("ttml", temp_dir)
                        with yt_dlp.YoutubeDL(ttml_opts) as ydl:
                            subtitles = await extractor.extract(ydl, info)
                    else:
                        # Для JSON3 ydl не выполняет скачивание, опции не важны
                        with yt_dlp.YoutubeDL({"quiet": True}) as ydl:
                            subtitles = await extractor.extract(ydl, info)

                    # Если субтитры найдены, прекращаем попытки
                    if subtitles and any(subtitles.values()):
                        self.log.success(
                            f"Субтитры успешно извлечены с помощью стратегии {format_name.upper()}."
                        )
                        return subtitles
                    else:
                        self.log.warning(
                            f"Стратегия {format_name.upper()} не нашла подходящих субтитров."
                        )

                except Exception as e:
                    self.log.warning(
                        f"Ошибка при использовании стратегии {format_name.upper()}: {e}. Пробуем следующую."
                    )

        # Если после всех попыток субтитров нет
        raise SubtitlesNotFoundError(
            "Не удалось извлечь субтитры ни одним из доступных способов."
        )

    async def _save_subtitles_to_db(
        self, info: dict, subtitles: dict, original_language: str
    ):
        """Сохраняет субтитры в их собственную кэш-таблицу."""
        async with self.db_session_factory() as db_session:
            await subtitles_service.save_subtitles_to_db(
                db_session,
                {
                    "video_id": info.get("id"),
                    "title": info.get("title", "Untitled Video"),
                    "original_language": original_language,
                    "upload_date": datetime.strptime(d, "%Y%m%d").date()
                    if (d := info.get("upload_date"))
                    else None,
                    "ru_subtitles": subtitles.get("ru"),
                    "en_subtitles": subtitles.get("en"),
                },
            )
            await db_session.commit()

    async def _execute_task(self) -> dict:
        """Выполняет основную логику извлечения субтитров."""
        video_info = await self._get_video_info()

        # Определяем оригинальный язык один раз
        original_language = self.lang_manager.find_original_language(video_info)

        extracted_subtitles = await self._try_extraction_strategies(
            video_info, original_language
        )

        # Сохраняем результат в БД
        await self._save_subtitles_to_db(
            video_info, extracted_subtitles, original_language
        )

        # Формируем и возвращаем результат для Task-а
        result_data = {
            "video_id": video_info.get("id"),
            "title": video_info.get("title", "Untitled Video"),
            "original_language": original_language,
            "upload_date": datetime.strptime(d, "%Y%m%d").date().isoformat()
            if (d := video_info.get("upload_date"))
            else None,
            "ru_subtitles": extracted_subtitles.get("ru"),
            "en_subtitles": extracted_subtitles.get("en"),
        }
        return result_data
