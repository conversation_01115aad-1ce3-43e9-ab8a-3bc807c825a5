# app/tasks/summarization.py

from app.tasks.summ_utils.orchestrator import SummarizationOrchestrator


async def summarize_text(ctx: dict, text: str, mode: str, task_id: str) -> None:
    """
    ARQ-задача для суммаризации текста.
    Создает и запускает экземпляр Orchestrator'а, который выполняет всю работу.
    """
    orchestrator = SummarizationOrchestrator(
        ctx=ctx,
        text=text,
        mode=mode,
        task_id=task_id,
    )
    await orchestrator.run()
