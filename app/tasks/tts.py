from app.tasks.tts_utils.orchestrator import TTSOrchestrator


async def synthesize_audio(ctx: dict, text: str, engine: str, task_id: str) -> None:
    """
    ARQ-задача для синтеза аудио из текста.
    Создает и запускает экземпляр Orchestrator'а, который выполняет всю работу.
    """
    orchestrator = TTSOrchestrator(
        ctx=ctx,
        text=text,
        engine=engine,
        task_id=task_id,
    )
    await orchestrator.run(arq_redis=ctx["redis"])
