# app/tasks/summ_utils/ai_client_factory.py
from typing import Dict, Any
from app.tasks.summ_utils.ai_client import GeminiAIClient
from app.tasks.summ_utils.openrouter_ai_client import OpenRouterAIClient


class AIClientFactory:
    """
    Фабрика для создания экземпляров AI-клиентов в зависимости от провайдера.
    """

    @staticmethod
    def create_client(genai_client, mode_config: Dict[str, Any]):
        """
        Создает и возвращает нужный AI-клиент.

        :param genai_client: Клиент Google GenAI (может быть None для других провайдеров).
        :param mode_config: Конфигурация режима суммаризации.
        :return: Экземпляр AI-клиента.
        """
        provider = mode_config.get("provider", "Gemini").lower()

        if provider == "openrouter":
            return OpenRouterAIClient(mode_config)
        elif provider == "gemini":
            if not genai_client:
                raise ValueError("genai_client required for Gemini provider")
            return GeminiAIClient(genai_client, mode_config)
        else:
            raise ValueError(f"Unknown AI provider specified: {provider}")
