import asyncio
from loguru import logger

from app.core.config_summaries import get_mode_config
from app.features.summarization import service as summarization_service
from app.features.tasks import service as task_service
from app.tasks.summ_utils.ai_client import GeminiAIClient
from app.tasks.summ_utils.openrouter_ai_client import OpenRouterAIClient
from app.tasks.summ_utils.exceptions import AIClientError, AIAPIError, NetworkError
from app.tasks.summ_utils.proxy_patcher import apply_http_proxy_patch


class SummarizationOrchestrator:
    """
    Управляет процессом суммаризации, строго следуя оригинальной логике
    повторных попыток и обработки фатальных ошибок.
    """

    MAX_TRIES = 3
    BACKOFF_FACTOR = 2

    def __init__(self, ctx: dict, text: str, mode: str, task_id: str):
        self.text = text
        self.mode = mode
        self.task_id = task_id
        self.db_session_factory = ctx["db_session_factory"]
        self.genai_client = ctx["genai_client"]
        self.log = logger.bind(
            job_id=ctx.get("job_id", "unknown"), task_id=self.task_id
        )

    async def _handle_error(self, error_message: str):
        """Централизованная обработка ошибок выполнения задачи."""
        self.log.error(f"Сбой задачи суммаризации: {error_message}")
        async with self.db_session_factory() as session:
            try:
                await task_service.store_task_error(
                    session, self.task_id, error_message
                )
                await task_service.update_task_status(
                    session, self.task_id, task_service.TaskStatus.FAILED
                )
                await session.commit()
            except Exception as db_exc:
                self.log.critical(
                    f"КРИТИЧЕСКАЯ ОШИБКА: Не удалось сохранить ошибку в БД. Ошибка БД: {db_exc}"
                )

    async def _save_success_result(self, summary: str):
        """Сохраняет успешный результат в БД."""
        text_hash = self.task_id[:16]

        summary_data = {
            "text_hash": text_hash,
            "original_text": self.text,
            "summary": summary,
            "mode": self.mode,
        }
        result_data = {"summary": summary}

        async with self.db_session_factory() as session:
            await summarization_service.save_summary_to_db(session, summary_data)
            await task_service.store_task_result(session, self.task_id, result_data)
            await task_service.update_task_status(
                session, self.task_id, task_service.TaskStatus.COMPLETED
            )
            await session.commit()
        self.log.info("Суммаризация успешно завершена и сохранена.")

    async def run(self):
        """Основной метод, запускающий процесс."""
        self.log.info(f"Начало суммаризации для режима '{self.mode}'.")

        async with self.db_session_factory() as session:
            await task_service.update_task_status(
                session, self.task_id, task_service.TaskStatus.PROCESSING
            )
            await session.commit()

        try:
            apply_http_proxy_patch()
            mode_config = get_mode_config(self.mode)

            # Dynamically select AI client based on provider
            provider = mode_config.get(
                "provider", "Gemini"
            )  # Default to Gemini if not specified
            if provider == "OpenRouter":
                # For OpenRouter, the client doesn't directly use genai_client, but requires api_key and base_url
                # These should be passed through mode_config or environment variables
                # For now, we assume mode_config contains all necessary for OpenRouterAIClient
                ai_client = OpenRouterAIClient(mode_config)
            else:
                ai_client = GeminiAIClient(self.genai_client, mode_config)

        except Exception as setup_exc:
            await self._handle_error(f"Ошибка на этапе подготовки: {setup_exc}")
            return

        for attempt in range(self.MAX_TRIES):
            try:
                summary = await ai_client.generate_summary(self.text)
                await self._save_success_result(summary)
                return

            except (
                NetworkError,
                AIClientError,
            ) as e:  # Теперь AIClientError также приводит к ретраю
                wait_time = self.BACKOFF_FACTOR ** (attempt + 1)
                if attempt + 1 >= self.MAX_TRIES:
                    error_msg = f"Сетевая или клиентская ошибка после {self.MAX_TRIES} попыток: {e}"
                    await self._handle_error(error_msg)
                    break
                self.log.warning(
                    f"Сетевая или клиентская ошибка, повторная попытка через {wait_time} секунд... [{attempt + 1}/{self.MAX_TRIES}] {e}"
                )
                await asyncio.sleep(wait_time)

            except AIAPIError as e:  # Все остальные ошибки от клиента (кроме NetworkError и AIClientError) - фатальны
                await self._handle_error(str(e))
                break
            except Exception as e:  # Непредвиденный catch-all
                await self._handle_error(f"Произошла непредвиденная ошибка: {e}")
                break
