# app/tasks/summ_utils/exceptions.py


class AIClientError(Exception):
    """Базовое исключение для ошибок, связанных с AI клиентом, не подлежащих ретраю."""

    pass


class AIResponseError(AIClientError):
    """
    Возбуждается, когда AI модель вернула пустой, заблокированный
    или некорректный ответ.
    """

    pass


class AIAPIError(AIClientError):
    """
    Возбуждается при ошибках на стороне API (например, ServerError).
    Эта ошибка не подлежит повторным попыткам.
    """

    pass


class NetworkError(Exception):
    """
    Возбуждается при сетевых ошибках (например, httpx.RequestError),
    которые могут быть исправлены повторной попыткой.
    """

    pass
