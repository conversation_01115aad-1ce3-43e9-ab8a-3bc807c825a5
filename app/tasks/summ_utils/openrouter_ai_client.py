import httpx
from typing import Dict, Any, List

from openai import AsyncOpenAI, APIError, APIConnectionError, RateLimitError
from loguru import logger

from app.core.exceptions import AIResponseError, AIAPIError, AIClientError, NetworkError
from app.core.config import settings  # Import settings


class OpenRouterAIClient:
    """
    Клиент для взаимодействия с OpenRouter API (OpenAI-совместимый).
    Адаптирован для использования OpenAI SDK.
    """

    def __init__(self, mode_config: Dict[str, Any]):
        # Получаем API ключ из глобальных настроек
        if settings.OPENROUTER_API_KEY is None:
            raise ValueError("OPENROUTER_API_KEY не установлен в переменных окружения.")
        self.api_key = settings.OPENROUTER_API_KEY

        self.base_url = mode_config.get("base_url", "https://openrouter.ai/api/v1")
        self.model_name = mode_config["model_name"]
        self.prompt_template = mode_config["prompt"]
        self.use_raw_text = mode_config.get("raw_text", False)

        # Извлекаем параметры генерации из объекта 'config', переданного в mode_config
        genai_config = mode_config.get("config")
        if genai_config:
            self.generation_params = {
                "temperature": genai_config.temperature,
                "top_p": genai_config.top_p,
                "max_tokens": genai_config.max_output_tokens,  # Сопоставляем с max_tokens для OpenAI
            }
        else:
            self.generation_params = {}  # Запасной вариант, если 'config' не предоставлен

        self.client = AsyncOpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
            timeout=httpx.Timeout(
                10.0, connect=5.0
            ),  # Добавляем таймаут для предотвращения бесконечных ожиданий
        )

    def _build_messages(self, text: str) -> List[Dict[str, str]]:
        """Формирует список сообщений для запроса к API OpenAI-совместимому."""
        if self.use_raw_text:
            return [{"role": "user", "content": text}]

        # Construct the formatted_text exactly as in the original Gemini client
        final_prompt_text = self.prompt_template.format(text=text)

        # Combine the original text and the final prompt text as per the original format
        combined_content = f"""----------------------\n\n{text}\n\n----------------------\n\n# Задание:\n{final_prompt_text}"""

        return [
            {"role": "user", "content": combined_content},
        ]

    def _validate_response(self, response) -> str:
        """Проверяет ответ от API и извлекает текст."""
        if (
            response.choices
            and response.choices[0].message
            and response.choices[0].message.content
        ):
            return response.choices[0].message.content.strip()

        error_details = []
        if response.choices:
            for i, choice in enumerate(response.choices):
                if hasattr(choice, "finish_reason"):
                    error_details.append(
                        f"Выбор {i}: причина завершения={choice.finish_reason}"
                    )
        error_info = (
            "; ".join(error_details)
            if error_details
            else "Подробная информация об ошибке отсутствует"
        )
        error_msg = f"AI-модель не смогла сгенерировать ответ: {error_info}"
        logger.error(f"API вернул пустой ответ. Детали: {error_info}")
        raise AIResponseError(error_msg)

    async def generate_summary(self, text: str) -> str:
        """
        Отправляет запрос на суммаризацию и возвращает результат.
        """
        messages = self._build_messages(text)
        logger.info(
            f"Отправка запроса к AI-модели '{self.model_name}' через OpenRouter..."
        )

        try:
            # OpenAI SDK handles async automatically when called with await
            completion = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                **self.generation_params,
            )
            return self._validate_response(completion)

        except RateLimitError as e:
            raise AIClientError(
                f"Превышен лимит запросов к API (OpenAI RateLimitError): {e}"
            ) from e
        except APIError as e:
            raise AIAPIError(
                f"Ошибка внешнего ИИ-сервиса (OpenAI APIError): {e}"
            ) from e
        except APIConnectionError as e:
            raise NetworkError(
                f"Ошибка сетевого подключения к API (OpenAI APIConnectionError): {e}"
            ) from e
        except httpx.HTTPStatusError as e:  # Специально для HTTP статус ошибок, которые не были перехвачены OpenAI SDK
            # На всякий случай проверяем 429, хотя RateLimitError должен был его поймать
            if e.response.status_code == 429:
                raise AIClientError(
                    f"Превышен лимит запросов к API (HTTP 429 Rate Limit - запасной): {e}"
                ) from e
            else:
                raise NetworkError(
                    f"Сетевая ошибка HTTP статус (httpx.HTTPStatusError): {e}"
                ) from e
        except httpx.RequestError as e:
            raise NetworkError(f"Сетевая ошибка (httpx.RequestError): {e}") from e
        except Exception as e:
            logger.error(
                f"Перехвачена непредвиденная ошибка при вызове AI (OpenRouter): {type(e).__name__}",
                exc_info=True,
            )
            raise AIClientError(
                f"Непредвиденная ошибка при вызове AI (OpenRouter): {e}"
            ) from e
