# app/tasks/summ_utils/ai_client.py

import asyncio
import httpx
from typing import Dict, Any

import google.genai as genai
from google.genai import types as genai_types
from google.genai import errors as genai_errors
from loguru import logger

from app.tasks.summ_utils.exceptions import (
    AIResponseError,
    AIAPIError,
    AIClientError,
    NetworkError,
)


class GeminiAIClient:
    """
    Клиент для взаимодействия с Google Gemini API.
    Строго следует логике вызовов и обработки ошибок из оригинального файла.
    """

    def __init__(self, genai_client: genai.client.Client, mode_config: Dict[str, Any]):
        self.client = genai_client
        self.model_name = mode_config["model_name"]
        self.prompt_template = mode_config["prompt"]
        # **ИСПРАВЛЕНИЕ**: Используем оригинальное имя словаря, чтобы избежать путаницы
        self.generation_params = mode_config.get("generation_config", {})
        self.use_raw_text = mode_config.get("raw_text", False)
        self.loop = asyncio.get_event_loop()

    def _build_contents(self, text: str) -> genai_types.Part:
        """Формирует контент для запроса к API, как в оригинале."""
        if self.use_raw_text:
            return genai_types.Part.from_text(text=text)

        formatted_text = f"""----------------------\n\n{text}\n\n----------------------\n\n# Задание:\n{self.prompt_template.format(text=text)}"""
        return genai_types.Part.from_text(text=formatted_text)

    def _validate_response(self, response: genai_types.GenerateContentResponse) -> str:
        """Проверяет ответ от API и извлекает текст, как в оригинале."""
        if hasattr(response, "text") and response.text:
            return response.text.strip()

        # Логика сбора детальной информации об ошибке, если текст отсутствует
        error_details = []
        if hasattr(response, "candidates") and response.candidates:
            for i, candidate in enumerate(response.candidates):
                if hasattr(candidate, "finish_reason"):
                    error_details.append(
                        f"Candidate {i}: finish_reason={candidate.finish_reason.name}"
                    )
                if hasattr(candidate, "safety_ratings"):
                    error_details.append(
                        f"Candidate {i}: safety_ratings={candidate.safety_ratings}"
                    )

        error_info = (
            "; ".join(error_details)
            if error_details
            else "No detailed error information"
        )
        error_msg = f"AI model failed to generate a response: {error_info}"
        logger.error(f"API вернул пустой ответ. Детали: {error_info}")
        raise AIResponseError(error_msg)

    async def generate_summary(self, text: str) -> str:
        """
        Отправляет запрос на суммаризацию и возвращает результат.
        Использует оригинальные имена параметров и логику обработки ошибок.
        """
        contents = self._build_contents(text)
        logger.info(f"Отправка запроса к AI-модели '{self.model_name}'...")

        try:
            # **ИСПРАВЛЕНИЕ**: Используем 'config' как имя параметра, а не 'generation_config'.
            # **ИСПРАВЛЕНИЕ**: Передаем 'contents' как один объект Part, а не список.
            response = await self.loop.run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model=self.model_name,
                    contents=contents,
                    config=self.generation_params,
                ),
            )
            return self._validate_response(response)

        # Обработка ошибок в строгом соответствии с оригиналом
        except (genai_errors.APIError, genai_errors.ServerError) as e:
            raise AIAPIError(f"Ошибка внешнего ИИ-сервиса: {e}") from e
        except (genai_errors.ClientError, ValueError) as e:
            raise AIClientError(f"Ошибка клиента или конфигурации: {e}") from e
        except (httpx.RemoteProtocolError, httpx.RequestError) as e:
            raise NetworkError(f"Сетевая ошибка: {e}") from e
        except Exception as e:
            logger.error(
                f"Перехвачена непредвиденная ошибка при вызове AI: {type(e).__name__}",
                exc_info=True,
            )
            raise AIClientError(f"Непредвиденная ошибка при вызове AI: {e}") from e
