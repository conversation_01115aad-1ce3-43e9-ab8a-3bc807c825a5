import httpx
from functools import wraps
from loguru import logger
from app.core.config import settings

_proxy_patched = False  # Глобальный флаг, чтобы избежать повторного патчинга


def apply_http_proxy_patch():
    """
    Проверяет настройки прокси и, если они заданы, применяет глобальный патч
    к клиентам httpx.Client и httpx.AsyncClient.
    Выполняется только один раз за сессию.
    """
    global _proxy_patched
    if _proxy_patched:
        logger.trace("Патч для прокси уже был применен. Пропускаем.")
        return

    proxy_url = settings.SOCKS5_PROXY_GEMINI
    if not proxy_url:
        logger.debug("Прокси для Gemini не настроен. Патчинг не требуется.")
        _proxy_patched = True  # Отмечаем, чтобы не проверять снова
        return

    logger.info(f"Обнаружен SOCKS5 прокси: {proxy_url}. Проверяем доступность...")

    try:
        with httpx.Client(proxy=proxy_url, timeout=10.0) as client:
            response = client.get("https://api.ipify.org?format=json")
            response.raise_for_status()
            proxy_ip = response.json().get("ip")
            logger.success(f"Тест прокси успешен! IP: {proxy_ip}. Применяем патч.")
    except Exception as e:
        logger.error(f"SOCKS5 прокси '{proxy_url}' недоступен: {e}")
        logger.warning("Приложение продолжит работу БЕЗ прокси для Gemini.")
        _proxy_patched = True  # Отмечаем, чтобы не пытаться снова
        return

    def create_patched_init(original_init):
        @wraps(original_init)
        def new_init(self, *args, **kwargs):
            if "proxy" not in kwargs:
                kwargs["proxy"] = proxy_url
                logger.trace(
                    f"Патч применен к {original_init.__qualname__}: добавлен прокси."
                )
            return original_init(self, *args, **kwargs)

        return new_init

    httpx.AsyncClient.__init__ = create_patched_init(httpx.AsyncClient.__init__)
    httpx.Client.__init__ = create_patched_init(httpx.Client.__init__)
    _proxy_patched = True
    logger.info("Глобальный патч для httpx успешно применен.")
