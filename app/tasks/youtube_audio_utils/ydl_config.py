from typing import Any, Dict, Optional
from loguru import logger
import os

from app.core.config import settings


class YTDLAudioLogger:
    """Направляет логи yt-dlp в loguru для консистентного логирования."""

    def debug(self, msg: str) -> None:
        if msg.startswith("[debug] "):
            logger.debug(f"[yt-dlp-audio] {msg[len('[debug] ') :]}")
        else:
            logger.debug(f"[yt-dlp-audio] {msg}")

    def warning(self, msg: str) -> None:
        logger.warning(f"[yt-dlp-audio] {msg}")

    def error(self, msg: str) -> None:
        logger.error(f"[yt-dlp-audio] {msg}")


def build_audio_ydl_options(
    download_dir: str, progress_hook: Optional[callable] = None
) -> Dict[str, Any]:
    """Собирает конфигурацию для yt-dlp для загрузки аудио."""
    opts = {
        "format": "bestaudio[ext=m4a]/bestaudio[ext=aac]/bestaudio/best",
        "outtmpl": os.path.join(download_dir, "%(id)s.%(ext)s"),
        "noplaylist": True,
        "quiet": True,
        "no_warnings": True,
        "logger": YTDLAudioLogger(),
        "postprocessors": [
            {
                "key": "FFmpegExtractAudio",
                "preferredcodec": "m4a",  # Prefer m4a (AAC) for better compatibility and quality
                "preferredquality": "best",
            }
        ],
    }

    if progress_hook:
        opts["progress_hooks"] = [progress_hook]

    # Добавляем прокси, если настроен
    if settings.SOCKS5_PROXY_YOUTUBE:
        opts["proxy"] = settings.SOCKS5_PROXY_YOUTUBE
        logger.debug(
            f"[yt-dlp-audio] Использование прокси: {settings.SOCKS5_PROXY_YOUTUBE}"
        )

    # Добавляем куки, если настроены
    if settings.YOUTUBE_COOKIES_FILE:
        opts["cookiefile"] = settings.YOUTUBE_COOKIES_FILE
        logger.debug(
            f"[yt-dlp-audio] Использование файла куки: {settings.YOUTUBE_COOKIES_FILE}"
        )

    return opts
