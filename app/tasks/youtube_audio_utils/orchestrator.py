import asyncio
import tempfile
from pathlib import Path
from loguru import logger
from typing import Callable

from app.features.youtube_audio import service as youtube_audio_service
from app.features.youtube_audio.schemas import YouTubeAudioResult
from app.core.exceptions import YouTubeAudioDownloadError, WorkerFatalError
from app.core.minio_client import minio_client
from app.tasks.youtube_audio_utils.extractors import YtDlpAudioExtractor
from app.tasks.base_orchestrator import BaseTaskOrchestrator

AUDIO_DOWNLOAD_ATTEMPTS = 3
AUDIO_DOWNLOAD_TIMEOUT = 90  # seconds


class YouTubeAudioOrchestrator(BaseTaskOrchestrator):
    def __init__(
        self,
        video_url: str,
        task_id: str,
        db_session_factory: Callable,
    ):
        super().__init__(task_id, db_session_factory)
        self.video_url = video_url
        self.download_dir = tempfile.mkdtemp(prefix="ytaudio_")
        self.extractor = YtDlpAudioExtractor(progress_hook=self._download_progress_hook)
        logger.debug(
            f"[АудиоОркестратор] Инициализировано: video_url={self.video_url}, task_id={self.task_id}, download_dir={self.download_dir}"
        )

    def _download_progress_hook(self, d):
        if d.get("status") == "downloading":
            logger.debug(
                f"[АудиоОркестратор] Загрузка {self.video_url}: {d.get('_percent_str')} из {d.get('_total_bytes_str')} со скоростью {d.get('_speed_str')}"
            )
        elif d.get("status") == "finished":
            logger.info(f"[АудиоОркестратор] Загрузка завершена: {d.get('filename')}")

    async def _convert_to_mp3(self, input_file_path: Path) -> Path:
        """Конвертирует аудиофайл в MP3 используя ffmpeg."""
        try:
            output_file_path = input_file_path.with_suffix(".mp3")

            # Используем ffmpeg для конвертации
            process = await asyncio.create_subprocess_exec(
                "ffmpeg",
                "-i",
                str(input_file_path),
                "-codec:a",
                "libmp3lame",
                "-b:a",
                "128k",
                str(output_file_path),
                "-y",  # -y для перезаписи файла
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"Ошибка ffmpeg при конвертации в MP3: {stderr.decode()}")
                raise YouTubeAudioDownloadError(
                    f"Ошибка конвертации в MP3: {stderr.decode()}"
                )

            logger.info(f"Успешно конвертировано в MP3: {output_file_path}")
            return output_file_path

        except Exception as e:
            logger.error(f"Ошибка при конвертации в MP3: {e}")
            raise YouTubeAudioDownloadError(f"Ошибка конвертации в MP3: {e}") from e

    def _get_content_type(self, file_extension: str) -> str:
        """Определяет MIME тип по расширению файла."""
        content_types = {
            ".mp3": "audio/mpeg",
            ".mp4": "audio/mp4",
            ".m4a": "audio/mp4",
            ".webm": "audio/webm",
            ".ogg": "audio/ogg",
            ".wav": "audio/wav",
        }
        return content_types.get(file_extension.lower(), "audio/octet-stream")

    async def _execute_task(self) -> dict:
        last_exc = None
        for attempt in range(1, AUDIO_DOWNLOAD_ATTEMPTS + 1):
            try:
                logger.info(
                    f"[АудиоОркестратор] Попытка {attempt}/{AUDIO_DOWNLOAD_ATTEMPTS} загрузки аудио для {self.video_url}"
                )
                audio_data = await asyncio.wait_for(
                    self.extractor.extract(self.video_url, self.download_dir),
                    timeout=AUDIO_DOWNLOAD_TIMEOUT,
                )

                # Обработка файлов и загрузка на S3
                original_file_path = Path(audio_data["file_path"])
                video_id = audio_data["video_id"]

                # Определяем MIME типы
                original_ext = original_file_path.suffix.lower()
                original_content_type = self._get_content_type(original_ext)

                # Загрузка оригинального файла на S3
                s3_url_original = None
                if minio_client.is_s3_available():
                    try:
                        original_data = original_file_path.read_bytes()
                        s3_object_name = (
                            f"youtube_audio/original/{video_id}{original_ext}"
                        )
                        s3_url_original = await minio_client.upload_file_safe(
                            file_data=original_data,
                            object_name=s3_object_name,
                            content_type=original_content_type,
                        )
                        if s3_url_original:
                            logger.info(
                                f"Оригинальный файл загружен на S3: {s3_url_original}"
                            )
                    except Exception as e:
                        logger.error(f"Ошибка загрузки оригинального файла на S3: {e}")

                # Конвертация в MP3 и загрузка на S3
                mp3_file_path = await self._convert_to_mp3(original_file_path)
                s3_url_mp3 = None
                if minio_client.is_s3_available():
                    try:
                        mp3_data = mp3_file_path.read_bytes()
                        s3_object_name_mp3 = f"youtube_audio/mp3/{video_id}.mp3"
                        s3_url_mp3 = await minio_client.upload_file_safe(
                            file_data=mp3_data,
                            object_name=s3_object_name_mp3,
                            content_type="audio/mpeg",
                        )
                        if s3_url_mp3:
                            logger.info(f"MP3 файл загружен на S3: {s3_url_mp3}")
                    except Exception as e:
                        logger.error(f"Ошибка загрузки MP3 файла на S3: {e}")

                # Обновляем данные для сохранения в БД
                updated_audio_data = audio_data.copy()
                updated_audio_data["s3_url_original"] = s3_url_original
                updated_audio_data["s3_url_mp3"] = s3_url_mp3

                # Если S3 доступен и файлы загружены, очищаем локальный путь
                if s3_url_original and s3_url_mp3:
                    updated_audio_data["file_path"] = ""  # Очищаем локальный путь
                    # Удаляем локальные файлы
                    try:
                        original_file_path.unlink(missing_ok=True)
                        mp3_file_path.unlink(missing_ok=True)
                        logger.info("Локальные файлы удалены после загрузки на S3")
                    except Exception as e:
                        logger.warning(f"Ошибка удаления локальных файлов: {e}")

                async with self.db_session_factory() as db_session:
                    youtube_audio_entry = await youtube_audio_service.save_audio_to_db(
                        db_session, updated_audio_data
                    )
                    result_data = YouTubeAudioResult(
                        video_id=youtube_audio_entry.video_id,
                        file_path=youtube_audio_entry.file_path,
                        file_name=youtube_audio_entry.file_name,
                        file_size_bytes=youtube_audio_entry.file_size_bytes,
                        created_at=youtube_audio_entry.created_at,
                        audio_stream_url=f"/ytaudio/{youtube_audio_entry.video_id}/stream",
                        s3_url_original=youtube_audio_entry.s3_url_original,
                        s3_url_mp3=youtube_audio_entry.s3_url_mp3,
                    ).model_dump(mode="json")
                    return result_data
            except asyncio.TimeoutError:
                logger.warning(
                    f"[АудиоОркестратор] Таймаут (>{AUDIO_DOWNLOAD_TIMEOUT}с) на попытке {attempt} для {self.video_url}"
                )
                last_exc = YouTubeAudioDownloadError(
                    f"Таймаут после {AUDIO_DOWNLOAD_TIMEOUT}с"
                )
            except YouTubeAudioDownloadError as e:
                logger.warning(
                    f"[АудиоОркестратор] Ошибка загрузки на попытке {attempt}: {e}"
                )
                last_exc = e
            except Exception as e:
                logger.error(
                    f"[АудиоОркестратор] Непредвиденная ошибка на попытке {attempt}: {e}"
                )
                last_exc = e
            if attempt == AUDIO_DOWNLOAD_ATTEMPTS:
                if isinstance(last_exc, WorkerFatalError):
                    raise last_exc
                elif isinstance(last_exc, YouTubeAudioDownloadError):
                    raise last_exc
                else:
                    raise WorkerFatalError(
                        f"Непредвиденная ошибка во время обработки аудио: {last_exc}"
                    ) from last_exc
