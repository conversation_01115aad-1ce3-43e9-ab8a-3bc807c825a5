import asyncio
import os
import tempfile
from pathlib import Path
from typing import AsyncGenerator
from loguru import logger
from arq.connections import ArqRedis

from app.features.tasks import service as task_service
from app.features.youtube_audio import service as youtube_audio_service
from app.core.exceptions import YouTubeAudioProcessingError, WorkerFatalError
from app.core.minio_client import minio_client

# Find ffmpeg path
FFMPEG_PATH = "ffmpeg"  # Try to run it this way, might work

# --- Encoding Parameters ---
ENCODING_BITRATE = "192k"
ENCODING_SAMPLE_RATE = "44100"
ENCODING_CHANNELS = "2"
CHUNK_SIZE = 8192  # Chunk size for reading from ffmpeg stdout (in bytes)


class AudioStreamConverter:
    def __init__(self, ctx, video_id, client_task_id):
        self.ctx = ctx
        self.video_id = video_id
        self.client_task_id = client_task_id
        self.db_session_factory = ctx["db_session_factory"]
        self.redis: ArqRedis = ctx["redis"]
        self.channel_name = f"ytaudio_stream:{video_id}:{client_task_id}"
        self.file_path = None
        self.temp_file_path = None  # Для временного файла, скачанного с S3

    async def _download_from_s3(self, s3_url: str) -> str:
        """Скачивает файл с S3 во временную директорию."""
        try:
            object_name = minio_client.extract_object_name_from_s3_url(s3_url)
            file_data = await minio_client.download_file(object_name)

            # Создаем временный файл
            temp_dir = tempfile.mkdtemp(prefix="ytaudio_s3_")
            temp_file_path = Path(temp_dir) / f"{self.video_id}_original"
            temp_file_path.write_bytes(file_data)

            self.temp_file_path = str(temp_file_path)
            logger.info(
                f"Файл скачан с S3 во временную директорию: {self.temp_file_path}"
            )
            return self.temp_file_path

        except Exception as e:
            logger.error(f"Ошибка скачивания файла с S3: {e}")
            raise YouTubeAudioProcessingError(f"Ошибка скачивания с S3: {e}") from e

    def _cleanup_temp_file(self):
        """Удаляет временный файл."""
        if self.temp_file_path and os.path.exists(self.temp_file_path):
            try:
                os.remove(self.temp_file_path)
                # Также удаляем временную директорию, если она пустая
                temp_dir = os.path.dirname(self.temp_file_path)
                try:
                    os.rmdir(temp_dir)
                except OSError:
                    pass  # Директория не пустая или другая ошибка
                logger.debug(f"Временный файл удален: {self.temp_file_path}")
            except Exception as e:
                logger.warning(f"Ошибка удаления временного файла: {e}")
            finally:
                self.temp_file_path = None

    async def _run_ffmpeg(self, input_path: str) -> asyncio.subprocess.Process:
        """Launches an ffmpeg process for encoding."""

        # Ensure input file exists before starting
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found at {input_path}")

        args = [
            FFMPEG_PATH,
            "-hide_banner",  # Hide ffmpeg version info
            "-loglevel",
            "error",  # Show only errors
            "-i",
            input_path,  # Input file
            "-vn",  # Disable video
            "-acodec",
            "libmp3lame",  # MP3 codec (lame - high quality)
            "-ar",
            ENCODING_SAMPLE_RATE,  # Sample rate
            "-ac",
            ENCODING_CHANNELS,  # Channels
            "-b:a",
            ENCODING_BITRATE,  # Audio bitrate
            "-f",
            "mp3",  # Output MP3 format
            "pipe:1",  # Output to stdout
        ]
        logger.info(f"Запуск команды FFmpeg: {' '.join(args)}")

        try:
            process = await asyncio.create_subprocess_exec(
                *args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,  # Capture stderr for logs/errors
            )
            return process
        except FileNotFoundError:
            logger.error(
                f"Исполняемый файл ffmpeg не найден по пути '{FFMPEG_PATH}'. Убедитесь, что ffmpeg установлен и прописан в PATH."
            )
            raise YouTubeAudioProcessingError(
                f"ffmpeg не найден по пути '{FFMPEG_PATH}'"
            )
        except Exception as e:
            logger.error(f"Не удалось запустить процесс ffmpeg: {e}")
            raise YouTubeAudioProcessingError(f"Не удалось запустить ffmpeg: {e}")

    async def _stream_mp3_data(
        self,
        process: asyncio.subprocess.Process,
    ) -> AsyncGenerator[bytes, None]:
        """Asynchronous generator for reading data from ffmpeg stdout."""
        try:
            while True:
                chunk = await process.stdout.read(CHUNK_SIZE)
                if not chunk:
                    logger.debug("Достигнут конец вывода FFmpeg stdout.")
                    break
                yield chunk
        except asyncio.CancelledError:
            logger.warning("Потоковая передача отменена клиентом.")
            if process.returncode is None:
                try:
                    process.terminate()
                    await asyncio.wait_for(process.wait(), timeout=2.0)
                    logger.debug("Процесс FFmpeg завершен из-за отмены клиентом.")
                except asyncio.TimeoutError:
                    logger.warning(
                        "FFmpeg не завершил работу корректно после отмены, принудительное завершение."
                    )
                    process.kill()
                except Exception as e:
                    logger.error(f"Ошибка при завершении ffmpeg после отмены: {e}")
            raise  # Re-raise exception for FastAPI to handle connection break
        except Exception as e:
            logger.error(f"Ошибка чтения из stdout ffmpeg: {e}")
            stderr_data = await process.stderr.read()
            if stderr_data:
                logger.error(
                    f"Вывод FFmpeg stderr: {stderr_data.decode(errors='ignore')}"
                )
            raise YouTubeAudioProcessingError(
                f"Ошибка во время потоковой передачи кодирования: {e}"
            )
        finally:
            return_code = await process.wait()
            if return_code != 0:
                stderr_data = await process.stderr.read()
                logger.error(f"Процесс FFmpeg завершился с кодом ошибки {return_code}.")
                if stderr_data:
                    logger.error(
                        f"FFmpeg stderr: {stderr_data.decode(errors='ignore')}"
                    )
            else:
                logger.debug("Процесс FFmpeg успешно завершен.")

    async def run(self):
        logger.info(
            f"ARQ задача 'convert_and_stream_audio' запущена для video_id: {self.video_id}, client_task_id: {self.client_task_id}"
        )
        try:
            async with self.db_session_factory() as db_session:
                audio_entry = await youtube_audio_service.get_audio_from_db(
                    db_session, self.video_id
                )
                if not audio_entry:
                    logger.error(
                        f"Кэшированная аудиозапись не найдена для video_id: {self.video_id}. Невозможно начать потоковую передачу."
                    )
                    await self.redis.publish(
                        self.channel_name, b"STREAM_ERROR: Audio file not found"
                    )
                    return  # Exit early

            # Определяем источник файла: S3 или локальный
            if audio_entry.s3_url_original:
                # Скачиваем файл с S3
                try:
                    self.file_path = await self._download_from_s3(
                        audio_entry.s3_url_original
                    )
                    logger.info(f"Используем файл с S3: {audio_entry.s3_url_original}")
                except Exception as e:
                    logger.error(f"Ошибка скачивания с S3: {e}")
                    await self.redis.publish(
                        self.channel_name,
                        f"STREAM_ERROR: S3 download failed: {str(e)}".encode(),
                    )
                    return
            else:
                # Используем локальный файл
                self.file_path = audio_entry.file_path
                if not self.file_path or not os.path.exists(self.file_path):
                    logger.error(
                        f"Локальный аудиофайл не существует по пути {self.file_path} для video_id: {self.video_id}"
                    )
                    await self.redis.publish(
                        self.channel_name,
                        b"STREAM_ERROR: Local audio file not found on disk",
                    )
                    return  # Exit early

            ffmpeg_process = await self._run_ffmpeg(self.file_path)

            async for chunk in self._stream_mp3_data(ffmpeg_process):
                await self.redis.publish(
                    self.channel_name, chunk
                )  # Publish chunk to Redis

            await self.redis.publish(
                self.channel_name, b"STREAM_END"
            )  # Signal end of stream
            logger.info(
                f"Завершена потоковая передача и опубликовано STREAM_END для video_id: {self.video_id}"
            )

        except YouTubeAudioProcessingError as e:
            logger.error(
                f"Ошибка обработки аудио YouTube для video_id {self.video_id}: {e}"
            )
            await self.redis.publish(
                self.channel_name, f"STREAM_ERROR: {str(e)}".encode()
            )
            async with self.db_session_factory() as db_session:
                task = await task_service.get_task_by_id(
                    db_session, self.client_task_id
                )
                if task and task.status != task_service.TaskStatus.COMPLETED:
                    await task_service.store_task_error(
                        db_session, self.client_task_id, str(e)
                    )
            raise WorkerFatalError(
                f"Не удалось обработать аудио для потоковой передачи: {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Непредвиденная ошибка произошла в convert_and_stream_audio для video_id {self.video_id}: {e}",
                exc_info=True,
            )
            await self.redis.publish(
                self.channel_name, f"STREAM_ERROR: {str(e)}".encode()
            )
            async with self.db_session_factory() as db_session:
                task = await task_service.get_task_by_id(
                    db_session, self.client_task_id
                )
                if task and task.status != task_service.TaskStatus.COMPLETED:
                    await task_service.store_task_error(
                        db_session, self.client_task_id, str(e)
                    )
            raise WorkerFatalError(
                f"Непредвиденная ошибка во время потоковой передачи аудио: {e}"
            ) from e
        finally:
            # Очищаем временный файл, если он был скачан с S3
            self._cleanup_temp_file()

            # Удаляем локальный файл только если он не был скачан с S3
            if (
                not self.temp_file_path
                and self.file_path
                and os.path.exists(self.file_path)
            ):
                youtube_audio_service.delete_audio_file(self.file_path)


async def convert_and_stream_audio(
    ctx: dict, video_id: str, client_task_id: str
) -> None:
    """
    ARQ-задача для потоковой конвертации аудио.
    Создает и запускает экземпляр AudioStreamConverter.
    """
    logger.info(
        f"Запуск конвертера для video_id: {video_id}, client_task_id: {client_task_id}"
    )
    converter = AudioStreamConverter(ctx, video_id, client_task_id)
    await converter.run()
