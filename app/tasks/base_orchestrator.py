# app/tasks/base_orchestrator.py
from abc import ABC, abstractmethod
from loguru import logger

from app.features.tasks import service as task_service
from arq.connections import ArqRedis
from app.core.exceptions import WorkerFatalError, WorkerTransientError


class BaseTaskOrchestrator(ABC):
    """
    Абстрактный базовый класс для всех оркестраторов задач.
    Инкапсулирует общую логику жизненного цикла задачи:
    - Обновление статуса на PROCESSING
    - Выполнение основной логики в _execute_task
    - Сохранение результата или ошибки
    - Централизованная обработка исключений, включая повторные попытки
    """

    # --- Параметры для повторных попыток ---
    MAX_RETRIES = 3
    RETRY_DELAY_SECONDS = 60  # 1 минута

    def __init__(self, task_id: str, db_session_factory, **kwargs):
        self.task_id = task_id
        self.db_session_factory = db_session_factory
        self.log = logger.bind(task_id=self.task_id)
        # Получаем номер текущей попытки из контекста ARQ
        self.current_retry = kwargs.get("job_try", 1) - 1

    @abstractmethod
    async def _execute_task(self) -> dict:
        """
        Основной метод, который должны реализовать дочерние классы.
        Содержит уникальную логику выполнения задачи.
        Должен возвращать словарь с результатом для сохранения в БД.
        """
        pass

    async def _save_success_result(self, result_data: dict):
        """Сохраняет успешный результат в БД."""
        async with self.db_session_factory() as session:
            await task_service.store_task_result(session, self.task_id, result_data)
            await task_service.update_task_status(
                session, self.task_id, task_service.TaskStatus.COMPLETED
            )
        self.log.info("Задача успешно завершена и результат сохранен.")

    async def _handle_error(self, error: Exception, arq_redis: ArqRedis):
        """Централизованная обработка ошибок выполнения задачи."""
        error_message = f"{type(error).__name__}: {error}"
        self.log.error(f"Ошибка выполнения задачи: {error_message}", exc_info=True)

        # Обработка временных ошибок с повторными попытками
        if isinstance(error, WorkerTransientError):
            if self.current_retry < self.MAX_RETRIES:
                self.log.warning(
                    f"Перехвачена временная ошибка. Попытка {self.current_retry + 1}/{self.MAX_RETRIES + 1}. "
                    f"Задача будет повторно поставлена в очередь через {self.RETRY_DELAY_SECONDS} сек."
                )
                # ARQ автоматически обработает повтор через `retry` в `on_error`
                # Просто выбрасываем исключение, чтобы ARQ его поймал
                raise error
            else:
                self.log.error(
                    f"Достигнут лимит повторных попыток ({self.MAX_RETRIES + 1}). "
                    f"Задача будет помечена как FAILED."
                )
                error_message = (
                    f"Достигнут лимит повторов. Последняя ошибка: {error_message}"
                )

        # Сохранение финальной ошибки (фатальной или после всех попыток)
        async with self.db_session_factory() as session:
            try:
                await task_service.store_task_error(
                    session, self.task_id, error_message
                )
            except Exception as db_exc:
                self.log.critical(
                    f"КРИТИЧЕСКАЯ ОШИБКА: Не удалось сохранить ошибку в БД. Ошибка БД: {db_exc}"
                )

        # Для фатальных ошибок, которые не должны приводить к повтору
        if isinstance(error, WorkerFatalError):
            self.log.critical(f"Фатальная ошибка, задача прервана: {error_message}")
            # Не выбрасываем, чтобы ARQ не пытался повторить

    async def _get_final_result(self, result_data: dict) -> dict:
        """Возвращает финальный результат для сохранения."""
        return result_data

    async def run(self, arq_redis: ArqRedis):
        """Основной метод, запускающий процесс выполнения задачи."""
        self.log.info(f"Начало обработки задачи (Попытка {self.current_retry + 1}).")

        async with self.db_session_factory() as session:
            await task_service.update_task_status(
                session, self.task_id, task_service.TaskStatus.PROCESSING
            )

        try:
            result = await self._execute_task()
            final_result = await self._get_final_result(result)
            await self._save_success_result(final_result)

        except Exception as e:
            # Передаем ошибку в новый обработчик
            await self._handle_error(e, arq_redis)
            # Если это была временная ошибка, она будет выброшена внутри _handle_error
            # чтобы ARQ мог ее поймать и выполнить retry.
