from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
import redis.asyncio as aioredis
from app.core.config import settings
from loguru import logger
from app.core.middleware import limiter, setup_rate_limiter, setup_cors

from app.features.subtitles.api import router as subtitles_router
from app.features.summarization.api import router as summarization_router
from app.features.tasks import api as tasks_api
from app.features.youtube_audio.api import router as youtube_audio_router
from app.features.tts.api import router as tts_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Инициализация Redis для SlowAPI...")
    try:
        app.state.redis = await aioredis.from_url(
            settings.REDIS_URL, encoding="utf-8", decode_responses=True
        )
        app.state.limiter = limiter
        logger.info("FastAPILimiter инициализирован.")
    except Exception as e:
        logger.error(f"Не удалось инициализировать Redis: {e}")

    yield

    logger.info("Закрытие соединения с Redis для SlowAPI...")
    try:
        await app.state.redis.close()
        logger.info("Соединение с Redis для SlowAPI закрыто.")
    except Exception as e:
        logger.error(f"Не удалось закрыть соединение с Redis: {e}")


app = FastAPI(title="Async API Server", lifespan=lifespan)

setup_cors(app)
setup_rate_limiter(app)

app.include_router(subtitles_router)
app.include_router(summarization_router)
app.include_router(tasks_api.router)
app.include_router(youtube_audio_router)
app.include_router(tts_router)


@app.get("/")
@limiter.limit("100/second")
async def root(request: Request):
    return {"message": "Server is running"}


@app.get("/ping")
@limiter.limit("100/second")
async def ping(request: Request):
    return {"message": "pong"}
