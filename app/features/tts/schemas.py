from typing import Optional
from sqlmodel import SQLModel


class TTSSubmitRequest(SQLModel):
    text: str
    engine: str = "edge"


class TTSSubmitResponse(SQLModel):
    text_hash: str
    status_link: str


class TTSResultResponse(SQLModel):
    text_hash: str
    status: str
    audio_file_link: Optional[str] = None
    detail: Optional[str] = None
    file_path: Optional[str] = None
    file_size_bytes: Optional[int] = None
