from typing import Dict, Any, Optional
from sqlmodel.ext.asyncio.session import AsyncSession
from fastapi import Depends, Request, Response, Body, Path, HTTPException, status
from fastapi.responses import StreamingResponse
from arq.connections import ArqRedis
from loguru import logger
import os

from app.features.base_api import BaseAPIEndpoint
from app.features.tasks.models import TaskType
from app.core.db import get_db_session, get_redis_pool
from app.core.minio_client import minio_client
from . import schemas, service
from .stream_consumer import RedisStreamConsumer  # <-- ДОБАВИТЬ ЭТОТ ИМПОРТ


class YouTubeAudioAPIEndpoint(BaseAPIEndpoint):
    ROUTER_PREFIX = "/ytaudio"
    ROUTER_TAGS = ["YouTube Audio"]
    TASK_TYPE = TaskType.YOUTUBE_AUDIO
    ARQ_TASK_NAME = "download_youtube_audio"

    def _get_task_id(self, request_data: schemas.YouTubeAudioRequest) -> str:
        cleaned_url = service.clean_youtube_url(str(request_data.url))
        video_id = service.extract_video_id(cleaned_url)
        if not video_id:
            return ""
        return f"{video_id}_audio"

    async def _check_cache(
        self,
        request: Request,
        db_session: AsyncSession,
        task_id: str,
        request_data: schemas.YouTubeAudioRequest,
    ) -> Optional[Dict[str, Any]]:
        video_id = task_id.replace("_audio", "")
        cached_result = await service.get_audio_from_db(db_session, video_id)
        if cached_result:
            cached_data = cached_result.model_dump()
            if "created_at" in cached_data and hasattr(
                cached_data["created_at"], "isoformat"
            ):
                cached_data["created_at"] = cached_data["created_at"].isoformat()

            # Определяем URL для стриминга в зависимости от наличия S3
            if cached_result.s3_url_mp3:
                # Если есть MP3 на S3, генерируем presigned URL
                try:
                    object_name = minio_client.extract_object_name_from_s3_url(
                        cached_result.s3_url_mp3
                    )
                    cached_data[
                        "audio_stream_url"
                    ] = await minio_client.get_presigned_url(
                        object_name, expires_seconds=3600
                    )
                except Exception as e:
                    logger.error(f"Error generating presigned URL for MP3: {e}")
                    # Fallback to local streaming endpoint
                    cached_data["audio_stream_url"] = str(
                        request.url_for("stream_youtube_audio", video_id=video_id)
                    )
            else:
                # Используем локальный эндпойнт для стриминга
                cached_data["audio_stream_url"] = str(
                    request.url_for("stream_youtube_audio", video_id=video_id)
                )
            return cached_data
        return None

    def _get_arq_task_params(
        self, task_id: str, request_data: schemas.YouTubeAudioRequest
    ) -> Dict[str, Any]:
        cleaned_url = service.clean_youtube_url(str(request_data.url))
        return {"video_url": cleaned_url, "task_id": task_id}

    def _register_routes(self):
        @self.router.post(
            "/",
            response_model=None,
            status_code=202,
            summary="Get YouTube audio for a video",
            description="Retrieves audio for a given YouTube video URL. If cached, enqueues a completed job and returns a result URL; otherwise, enqueues a job for processing and returns a task ID and status URL.",
        )
        async def get_youtube_audio(
            request: Request,
            response: Response,
            req_body: schemas.YouTubeAudioRequest = Body(
                ..., example={"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
            ),
            db_session: AsyncSession = Depends(get_db_session),
            redis: ArqRedis = Depends(get_redis_pool),
        ):
            return await self._handle_request(
                request, response, req_body, db_session, redis
            )


# Создаем экземпляр и экспортируем роутер
endpoint = YouTubeAudioAPIEndpoint()
router = endpoint.router


@router.get(
    "/{video_id}/stream",
    summary="Stream YouTube audio as MP3",
    description="Streams the downloaded YouTube audio in MP3 format. Initiates conversion if not already streaming.",
    response_class=StreamingResponse,
    responses={
        status.HTTP_200_OK: {
            "content": {"audio/mpeg": {}},
            "description": "MP3 audio stream.",
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Audio for video ID not found or not yet processed."
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "Internal server error during streaming or conversion."
        },
    },
)
async def stream_youtube_audio(
    request: Request,
    video_id: str = Path(..., description="The ID of the YouTube video."),
    db_session: AsyncSession = Depends(get_db_session),
    redis: ArqRedis = Depends(get_redis_pool),
):
    logger.info(f"Запрос на стриминг для video_id: {video_id}")

    # 1. Проверка наличия аудио в БД
    audio_entry = await service.get_audio_from_db(db_session, video_id)
    if not audio_entry:
        logger.warning(f"Аудиофайл не найден в БД для video_id: {video_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Аудио не найдено или еще не обработано.",
        )

    # 2. Проверяем наличие MP3 на S3
    if audio_entry.s3_url_mp3:
        try:
            # Если есть MP3 на S3, перенаправляем на presigned URL
            object_name = minio_client.extract_object_name_from_s3_url(
                audio_entry.s3_url_mp3
            )
            presigned_url = await minio_client.get_presigned_url(
                object_name, expires_seconds=3600
            )

            from fastapi.responses import RedirectResponse

            return RedirectResponse(url=presigned_url, status_code=302)
        except Exception as e:
            logger.error(f"Error generating presigned URL for MP3: {e}")
            # Fallback to local streaming

    # 3. Проверяем наличие локального файла для fallback
    if not audio_entry.file_path or not os.path.exists(audio_entry.file_path):
        logger.warning(f"Ни S3, ни локальный файл не доступны для video_id: {video_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Аудио не найдено или еще не обработано.",
        )

    # 2. Генерация ID сессии и постановка задачи в очередь (остается без изменений)
    client_task_id = f"stream_session_{os.urandom(16).hex()}"
    channel_name = f"ytaudio_stream:{video_id}:{client_task_id}"

    await redis.enqueue_job(
        "convert_and_stream_audio",
        video_id=video_id,
        client_task_id=client_task_id,
        _job_id=client_task_id,
    )

    # 3. Создание и использование нашего нового Consumer-а
    consumer = RedisStreamConsumer(redis_pool=redis, channel_name=channel_name)

    # 4. Формирование ответа (стало проще)
    base_filename = (
        os.path.splitext(audio_entry.file_name)[0]
        if audio_entry.file_name
        else video_id
    )
    output_filename = f"{base_filename}.mp3"

    return StreamingResponse(
        consumer.stream_generator(),  # <-- ВСЯ МАГИЯ ЗДЕСЬ!
        media_type="audio/mpeg",
        headers={"Content-Disposition": f'attachment; filename="{output_filename}"'},
    )
