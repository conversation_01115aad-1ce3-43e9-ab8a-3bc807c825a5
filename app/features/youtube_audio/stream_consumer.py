# app/features/youtube_audio/stream_consumer.py
import asyncio
import redis.asyncio as aioredis
from loguru import logger


class RedisStreamConsumer:
    """
    Управляет подпиской на Redis канал и предоставляет асинхронный генератор
    для потоковой передачи данных клиенту.
    """

    def __init__(self, redis_pool: aioredis.Redis, channel_name: str):
        self.redis = redis_pool
        self.channel_name = channel_name
        self.pubsub = self.redis.pubsub()
        self.log = logger.bind(channel=self.channel_name)

    async def _subscribe(self):
        await self.pubsub.subscribe(self.channel_name)
        self.log.info("Успешно подписан на канал Redis.")

    async def _listen(self):
        """
        Основной цикл прослушивания сообщений из Redis.
        Использует yield для создания асинхронного генератора.
        """
        try:
            # Первое сообщение - это подтверждение подписки, его можно проигнорировать
            # await self.pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)

            while True:
                # Ожидаем сообщение с таймаутом
                message = await self.pubsub.get_message(
                    ignore_subscribe_messages=True, timeout=20.0
                )
                if message:
                    data = message["data"]
                    if data == b"STREAM_END":
                        self.log.info("Получен сигнал STREAM_END. Завершение потока.")
                        break  # Выход из цикла завершит генератор

                    elif data.startswith(b"STREAM_ERROR:"):
                        error_msg = (
                            data.decode(errors="ignore")
                            .replace("STREAM_ERROR:", "")
                            .strip()
                        )
                        self.log.error(f"Получена ошибка из воркера: {error_msg}")
                        # Мы не можем просто бросить HTTPException здесь, т.к. заголовки уже отправлены.
                        # Просто прерываем поток. Клиент получит оборванное соединение.
                        break

                    else:
                        yield data
                else:
                    # Таймаут. Это может означать, что воркер "завис" или просто долго обрабатывает.
                    self.log.warning(
                        "Таймаут ожидания сообщения из Redis. Поток может быть остановлен."
                    )
                    # Можно добавить логику проверки статуса задачи в ARQ, но пока просто завершим поток.
                    break

        except asyncio.CancelledError:
            self.log.warning("Клиент отменил соединение.")
            # Это нормальная ситуация, просто выходим
        except Exception as e:
            self.log.error(
                f"Непредвиденная ошибка в цикле прослушивания Redis: {e}", exc_info=True
            )
        finally:
            self.log.info("Отписка от канала Redis.")
            await self.pubsub.unsubscribe(self.channel_name)
            await self.pubsub.close()

    async def stream_generator(self):
        """
        Главный метод-генератор, который нужно передавать в StreamingResponse.
        """
        await self._subscribe()
        async for chunk in self._listen():
            yield chunk
