from pydantic import HttpUrl, Field
from sqlmodel import SQLModel
from datetime import datetime
from typing import Optional


class YouTubeAudioRequest(SQLModel):
    url: HttpUrl = Field(..., example="https://www.youtube.com/watch?v=dQw4w9WgXcQ")


class YouTubeAudioResult(SQLModel):
    video_id: str
    file_path: str
    file_name: str
    file_size_bytes: int
    created_at: datetime
    audio_stream_url: str  # URL for streaming (local endpoint or S3 presigned URL)
    s3_url_original: Optional[str] = None  # S3 URL for original format
    s3_url_mp3: Optional[str] = None  # S3 URL for MP3 format
