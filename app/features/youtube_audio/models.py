from sqlmodel import SQLModel, <PERSON>
from datetime import datetime
from typing import Optional


class YouTubeAudio(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    video_id: str = Field(index=True, unique=True, nullable=False)
    # path to the downloaded audio file (deprecated, will be removed)
    file_path: str = Field(nullable=False)
    file_name: str = Field(nullable=False)
    file_size_bytes: int = Field(nullable=False)
    # S3 URLs for original and converted files
    s3_url_original: Optional[str] = Field(
        default=None, nullable=True
    )  # Original format (webm/m4a)
    s3_url_mp3: Optional[str] = Field(
        default=None, nullable=True
    )  # Converted MP3 format
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)

    class Config:
        json_schema_extra = {
            "example": {
                "video_id": "dQw4w9WgXcQ",
                "file_path": "/tmp/dQw4w9WgXcQ.webm",
                "file_name": "<PERSON> - Never Gonna Give You Up (Official Music Video).webm",
                "file_size_bytes": 4500000,
                "created_at": "2023-10-27T10:00:00Z",
            }
        }
