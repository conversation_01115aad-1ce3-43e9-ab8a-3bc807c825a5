from typing import Dict, Any, Optional

from sqlmodel.ext.asyncio.session import AsyncSession
from sqlmodel import select
from loguru import logger
import redis.asyncio as redis
import json

from .models import Task, TaskStatus, TaskType
from app.core.db import get_redis_pool


async def _publish_task_update(redis_pool: redis.Redis, task: Task) -> None:
    """Publishes task update to Redis Pub/Sub channel."""
    channel_name = f"task_updates:{task.id}"
    # Use task.model_dump_json() to get a JSON string representation
    # of the Task object, including nested fields if any, and handling enums.
    message = task.model_dump(mode="json")
    if task.result and "get_url" in task.result:
        message["result"]["get_url"] = task.result["get_url"]
    await redis_pool.publish(channel_name, json.dumps(message))
    logger.debug(f"Опубликовано обновление для задачи {task.id} в канал {channel_name}")


async def create_task(
    db_session: AsyncSession, task_type: TaskType, task_id: str
) -> Task:
    """Creates a new task with a specific ID if it doesn't already exist.

    If a task with the same ID exists but was last updated more than 5 minutes ago,
    it will be deleted and a new task will be created.
    """
    from datetime import datetime, timedelta

    # First, try to get the task by its ID
    existing_task = await get_task_by_id(db_session, task_id)

    if existing_task:
        # Если задача уже завершена, она является постоянной записью. Просто возвращаем ее.
        if existing_task.status == TaskStatus.COMPLETED:
            logger.debug(f"Возвращаю существующую завершенную задачу {task_id}.")
            return existing_task

        # Проверяем "зависшие" задачи (не COMPLETED), которые давно не обновлялись.
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        if existing_task.updated_at < five_minutes_ago:
            logger.debug(
                f"Устаревшая задача {task_id} (статус: {existing_task.status.value}) "
                f"не обновлялась более 5 минут. Создаю новую..."
            )
            # Удаляем старую "зависшую" задачу
            await db_session.delete(existing_task)
            await db_session.commit()
            logger.debug(f"Старая задача {task_id} удалена.")
        else:
            logger.debug(
                f"Задача с ID {task_id} уже существует (статус: {existing_task.status.value}) "
                f"и была недавно обновлена. Возвращаю существующую задачу."
            )
            return existing_task

    # Create a new task (either didn't exist or was deleted due to being stale)
    logger.debug(f"Создание новой задачи с ID: {task_id} типа: {task_type}")
    new_task = Task(id=task_id, task_type=task_type)
    db_session.add(new_task)
    await db_session.commit()
    await db_session.refresh(new_task)
    redis_pool = await get_redis_pool()
    await _publish_task_update(redis_pool, new_task)
    return new_task


async def get_task_by_id(db_session: AsyncSession, task_id: str) -> Optional[Task]:
    """Retrieves a task by its ID."""
    result = await db_session.execute(select(Task).where(Task.id == task_id))
    task = result.scalar_one_or_none()
    if not task:
        logger.debug(f"Задача с ID {task_id} не найдена.")
    return task


async def update_task_status(
    db_session: AsyncSession, task_id: str, status: TaskStatus
) -> Optional[Task]:
    """Updates the status of a task."""
    task = await get_task_by_id(db_session, task_id)
    if task:
        task.status = status
        await db_session.commit()
        await db_session.refresh(task)
        logger.debug(f"Обновлен статус задачи {task_id} на {status.value}")
        redis_pool = await get_redis_pool()
        await _publish_task_update(redis_pool, task)
        return task
    return None


async def store_task_result(
    db_session: AsyncSession, task_id: str, result_data: Dict[str, Any]
) -> Optional[Task]:
    """Stores the result of a completed task."""
    task = await get_task_by_id(db_session, task_id)
    if task:
        task.result = result_data
        task.status = TaskStatus.COMPLETED
        await db_session.commit()
        await db_session.refresh(task)
        logger.info(
            f"Результат задачи {task_id} сохранен, задача помечена как ЗАВЕРШЕНО."
        )
        redis_pool = await get_redis_pool()
        await _publish_task_update(redis_pool, task)
        return task
    return None


async def store_task_error(
    db_session: AsyncSession, task_id: str, error_message: str
) -> Optional[Task]:
    """Stores an error message for a failed task."""
    task = await get_task_by_id(db_session, task_id)
    if task:
        task.error = error_message
        task.status = TaskStatus.FAILED
        await db_session.commit()
        await db_session.refresh(task)
        logger.error(f"Сохранена ошибка для задачи {task_id}: {error_message}")
        redis_pool = await get_redis_pool()
        await _publish_task_update(redis_pool, task)
        return task
    return None
