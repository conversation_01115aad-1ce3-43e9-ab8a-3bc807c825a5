from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from sqlmodel import Field, SQLModel, JSON, Column


class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskType(str, Enum):
    SUBTITLES = "SUBTITLES"
    SUMMARIZATION = "SUMMARIZATION"
    YOUTUBE_AUDIO = "YOUTUBE_AUDIO"
    TTS = "TTS"


class Task(SQLModel, table=True):
    id: str = Field(primary_key=True, index=True)
    status: TaskStatus = Field(default=TaskStatus.PENDING, index=True)
    task_type: TaskType = Field(index=True)
    result: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    error: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column_kwargs={"onupdate": datetime.utcnow},
        nullable=False,
    )
