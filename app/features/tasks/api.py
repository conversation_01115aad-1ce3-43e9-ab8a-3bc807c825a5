from fastapi import APIRouter, Depends, HTTPException, Request
from sqlmodel.ext.asyncio.session import AsyncSession
from sse_starlette.sse import EventSourceResponse
from starlette.responses import JSONResponse
import asyncio
import json
from loguru import logger
from typing import AsyncGenerator
import redis.asyncio as redis

from app.core.db import get_db_session, get_redis_pool, AsyncSessionFactory
from . import schemas, service
from .models import TaskStatus
from app.core.middleware import limiter
from app.core.config import settings

router = APIRouter(prefix="/tasks", tags=["Tasks"])


# Add CORS headers for SSE endpoint
@router.options("/{task_id}/sse")
async def options_task_sse():
    response = JSONResponse(content={"message": "CORS preflight"})

    # Use CORS settings from config
    response.headers["Access-Control-Allow-Origin"] = settings.CORS_ORIGINS
    response.headers["Access-Control-Allow-Methods"] = settings.CORS_ALLOW_METHODS
    response.headers["Access-Control-Allow-Headers"] = settings.CORS_ALLOW_HEADERS
    response.headers["Access-Control-Allow-Credentials"] = str(
        settings.CORS_ALLOW_CREDENTIALS
    ).lower()

    return response


@router.get(
    "/{task_id}/status",
    response_model=schemas.TaskStatusResponse,
    summary="Get Task Status",
    description="Poll this endpoint to check the status of a background task.",
)
async def get_task_status(
    request: Request,
    task_id: str,
    db_session: AsyncSession = Depends(get_db_session),
):
    task = await service.get_task_by_id(db_session, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    response = schemas.TaskStatusResponse(
        task_id=task.id,
        status=task.status,
        error=task.error,
        sse_url=str(request.url_for("task_sse", task_id=task.id)),
    )

    if task.status == TaskStatus.COMPLETED:
        response.result_url = str(request.url_for("get_task_result", task_id=task.id))

    return response


@router.get(
    "/{task_id}/result",
    response_model=schemas.TaskResultResponse,
    summary="Get Task Result",
    description="Fetch the result of a completed task.",
    name="get_task_result",
)
@limiter.limit("10/second")
async def get_task_result(
    request: Request,
    task_id: str,
    db_session: AsyncSession = Depends(get_db_session),
):
    task = await service.get_task_by_id(db_session, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if task.status != TaskStatus.COMPLETED:
        raise HTTPException(
            status_code=400,
            detail=f"Task is not completed yet. Current status: {task.status}",
        )

    return schemas.TaskResultResponse(
        task_id=task.id, status=task.status, result=task.result
    )


@router.get(
    "/{task_id}/sse",
    summary="Subscribe to Task Updates via Server-Sent Events",
    description="Establishes an SSE connection to receive real-time updates on a task's status and progress.",
)
@limiter.limit("10/second")
async def task_sse(
    task_id: str,
    request: Request,
    redis_pool: redis.Redis = Depends(get_redis_pool),
) -> EventSourceResponse:
    async def event_generator() -> AsyncGenerator[str, None]:
        # --- НАЧАЛО ИЗМЕНЕНИЙ ---
        # Сначала проверим начальный статус задачи. Если она уже завершена,
        # отправим финальный статус и сразу закроем соединение.
        async with AsyncSessionFactory() as db_session:
            initial_task = await service.get_task_by_id(db_session, task_id)
            if not initial_task:
                yield json.dumps({"event": "error", "data": "Task not found"})
                return  # Используем return для выхода из генератора

            if initial_task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                logger.info(
                    f"Задача {task_id} уже завершена. Отправка конечного статуса и закрытие SSE."
                )
                data = {
                    "task_id": initial_task.id,
                    "status": initial_task.status.value,
                    "error": initial_task.error,
                }
                if initial_task.status == TaskStatus.COMPLETED:
                    data["result_url"] = str(
                        request.url_for("get_task_result", task_id=task_id)
                    )

                yield json.dumps({"event": "update", "data": data})
                return  # Выходим из генератора, соединение закроется
        # --- КОНЕЦ ПРЕДВАРИТЕЛЬНОЙ ПРОВЕРКИ ---

        channel_name = f"task_updates:{task_id}"
        pubsub = redis_pool.pubsub()
        await pubsub.subscribe(channel_name)
        logger.info(f"SSE клиент подписался на канал: {channel_name}")

        try:
            while True:
                if await request.is_disconnected():
                    logger.info(f"SSE клиент отключился от {channel_name}")
                    break

                # Убираем лишний запрос к БД в цикле, полагаемся на сообщения из Redis
                message = await pubsub.get_message(
                    ignore_subscribe_messages=True,
                    timeout=1.0,  # Используем True, чтобы не обрабатывать сообщение о подписке
                )
                if message and message["type"] == "message":
                    logger.debug(
                        f"SSE получил уведомление для задачи {task_id}. Обработка данных из Redis."
                    )

                    try:
                        task_data_json = message["data"].decode("utf-8")
                        task_data = json.loads(task_data_json)

                        data = {
                            "task_id": task_data.get("id", task_id),
                            "status": task_data.get("status"),
                            "error": task_data.get(
                                "error"
                            ),  # Ошибку можно получить из эндпоинта статуса
                        }

                        if data["status"] == TaskStatus.COMPLETED.value:
                            data["result_url"] = str(
                                request.url_for("get_task_result", task_id=task_id)
                            )

                        yield json.dumps({"event": "update", "data": data})

                        # === КЛЮЧЕВОЕ ИСПРАВЛЕНИЕ ===
                        # Проверяем, достигла ли задача финального статуса. Если да, выходим из цикла.
                        status_str = task_data.get("status")
                        if status_str in [
                            TaskStatus.COMPLETED.value,
                            TaskStatus.FAILED.value,
                        ]:
                            logger.info(
                                f"Задача {task_id} достигла конечного статуса ({status_str}). Завершение SSE потока."
                            )
                            break  # Прерываем цикл while, что приведет к закрытию соединения

                    except json.JSONDecodeError:
                        logger.error(
                            f"Не удалось декодировать JSON из сообщения Redis для задачи {task_id}"
                        )
                        yield json.dumps(
                            {
                                "event": "error",
                                "data": "Не удалось разобрать обновление задачи",
                            }
                        )
                        break
                    except Exception as e:
                        logger.error(
                            f"Непредвиденная ошибка при обработке сообщения Redis для задачи {task_id}: {e}",
                            exc_info=True,
                        )
                        yield json.dumps(
                            {
                                "event": "error",
                                "data": f"Ошибка обработки обновления: {type(e).__name__}",
                            }
                        )
                        break

                # Небольшая задержка, чтобы не нагружать CPU, если нет сообщений
                await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            logger.info(f"Поток SSE для задачи {task_id} отменен сервером.")
        except Exception as e:
            logger.error(f"Ошибка потока SSE для задачи {task_id}: {e}")
            yield json.dumps({"event": "error", "data": str(e)})
        finally:
            await pubsub.unsubscribe(channel_name)
            logger.info(f"SSE клиент отписан от канала: {channel_name}")

    # Configure CORS headers for SSE response using settings from config
    response = EventSourceResponse(event_generator())
    response.headers["Access-Control-Allow-Origin"] = settings.CORS_ORIGINS
    response.headers["Access-Control-Allow-Methods"] = settings.CORS_ALLOW_METHODS
    response.headers["Access-Control-Allow-Headers"] = settings.CORS_ALLOW_HEADERS
    response.headers["Access-Control-Allow-Credentials"] = str(
        settings.CORS_ALLOW_CREDENTIALS
    ).lower()
    return response
