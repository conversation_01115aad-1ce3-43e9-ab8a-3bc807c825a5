from typing import Optional
from pydantic import BaseModel, Field
from .models import TaskStatus


class TaskCreateResponse(BaseModel):
    task_id: str
    status_url: str = Field(..., description="URL to check the task status.")
    sse_url: str = Field(
        ..., description="URL to subscribe to real-time task updates via SSE."
    )

    def model_dump(self, **kwargs):
        data = super().model_dump(**kwargs)
        data["task_id"] = str(self.task_id)
        return data


class TaskStatusResponse(BaseModel):
    task_id: str
    status: TaskStatus
    result_url: Optional[str] = Field(
        None, description="URL to get the result once the task is completed."
    )
    sse_url: str = Field(
        ..., description="URL to subscribe to real-time task updates via SSE."
    )
    error: Optional[str] = Field(None, description="Error message if the task failed.")

    def model_dump(self, **kwargs):
        data = super().model_dump(**kwargs)
        data["task_id"] = str(self.task_id)
        if self.status:
            data["status"] = self.status.value
        return data


class TaskResultResponse(BaseModel):
    task_id: str
    status: TaskStatus
    result: Optional[dict]
