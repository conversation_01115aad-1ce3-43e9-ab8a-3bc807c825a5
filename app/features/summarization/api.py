from typing import Dict, Any, Optional
from sqlmodel.ext.asyncio.session import AsyncSession
from fastapi import Depends, Request, Response, Body
from arq.connections import ArqRedis
from loguru import logger
import urllib.parse
import hashlib

from app.features.base_api import BaseAPIEndpoint
from app.features.tasks.models import TaskType
from app.core.db import get_db_session, get_redis_pool
from . import schemas, service


class SummarizationAPIEndpoint(BaseAPIEndpoint):
    ROUTER_PREFIX = "/summarize"
    ROUTER_TAGS = ["Summarization"]
    TASK_TYPE = TaskType.SUMMARIZATION
    ARQ_TASK_NAME = "summarize_text"

    def _urlsafe_mode(self, mode: str) -> str:
        """Encode mode to be safe for use in URLs and as part of task_id."""
        return urllib.parse.quote(mode.replace("#", "_"), safe="")

    def _get_text_md5hash(self, text: str) -> str:
        """Generate MD5 hash of the input text"""
        return hashlib.md5(text.strip().encode("utf-8")).hexdigest()

    def _get_task_id(self, request_data: schemas.SummarizationRequest) -> str:
        text_hash = service.generate_text_xxhash(request_data.text)
        mode = request_data.mode
        safe_mode = self._urlsafe_mode(mode)
        return f"{text_hash}_{safe_mode}"

    async def _check_cache(
        self,
        request: Request,
        db_session: AsyncSession,
        task_id: str,
        request_data: schemas.SummarizationRequest,
    ) -> Optional[Dict[str, Any]]:
        text_hash = service.generate_text_xxhash(request_data.text)
        mode = request_data.mode
        cached_result = await service.get_summary_from_db(db_session, text_hash, mode)

        if not cached_result:
            # If not found with xxhash, try with legacy md5 hash
            md5_text_hash = self._get_text_md5hash(request_data.text)
            logger.debug(
                f"Ищем в кеше по устаревшему md5 хэшу для task_id: {md5_text_hash}_{self._urlsafe_mode(mode)}."
            )
            cached_result = await service.get_summary_from_db(
                db_session, md5_text_hash, mode
            )
            if cached_result:
                logger.info(
                    f"Найден результат по md5 хэшу для task_id: {md5_text_hash}_{self._urlsafe_mode(mode)}. Обновляем запись с xxhash."
                )
                # If found with MD5, create a new entry with xxhash for future requests
                await service.save_summary_to_db(
                    db_session,
                    {
                        "text_hash": text_hash,
                        "mode": mode,
                        "summary": cached_result.summary,
                        "original_text": request_data.text,
                    },
                )
        if cached_result:
            return cached_result.model_dump()
        return None

    def _get_arq_task_params(
        self, task_id: str, request_data: schemas.SummarizationRequest
    ) -> Dict[str, Any]:
        return {
            "text": request_data.text,
            "mode": request_data.mode,
            "task_id": task_id,
        }

    def _register_routes(self):
        @self.router.post(
            "/",
            response_model=None,
            status_code=202,
            summary="Summarize text",
            description="Generates a summary for the provided text. If cached, enqueues a completed job and returns a result URL; otherwise, enqueues a job for processing and returns a task ID and status URL.",
        )
        async def get_summary(
            request: Request,
            response: Response,
            req_body: schemas.SummarizationRequest = Body(
                ...,
                example={
                    "text": "FastAPI is a modern, fast (high-performance) web framework...",
                    "mode": "default",
                },
            ),
            db_session: AsyncSession = Depends(get_db_session),
            redis: ArqRedis = Depends(get_redis_pool),
        ):
            return await self._handle_request(
                request, response, req_body, db_session, redis
            )


# Создаем экземпляр и экспортируем роутер
endpoint = SummarizationAPIEndpoint()
router = endpoint.router
