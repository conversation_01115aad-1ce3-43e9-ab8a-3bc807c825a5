import hashlib
from typing import Optional

from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.features.summarization.models import SummarizationCache
from app.features.summarization.schemas import (
    SummarizationResponse,
)
from loguru import logger
from sqlalchemy.dialects.postgresql import insert
import xxhash


def generate_text_hash(text: str) -> str:
    """Generates a SHA256 hash for the given text."""
    return hashlib.sha256(text.strip().encode("utf-8")).hexdigest()


def generate_text_xxhash(text: str) -> str:
    """Generates a XXH3 hash for the given text."""
    return xxhash.xxh3_64_hexdigest(text.strip().encode("utf-8"))


async def get_summary_from_db(
    session: AsyncSession, text_hash: str, mode: str
) -> Optional[SummarizationResponse]:
    """Retrieves a summarization from the database cache by text hash and mode."""
    logger.debug(f"Проверка кэша суммаризации для hash={text_hash}, mode={mode}")
    statement = select(SummarizationCache).where(
        SummarizationCache.text_hash == text_hash, SummarizationCache.mode == mode
    )

    # Execute the query using async session
    result = await session.execute(statement)
    cached_summary = result.scalars().first()

    if cached_summary:
        logger.debug(f"Найдена в кеше суммаризация для hash={text_hash}, mode={mode}")
        return SummarizationResponse(summary=cached_summary.summary)

    logger.debug(f"Промах кэша суммаризации для hash={text_hash}, mode={mode}")
    return None


async def save_summary_to_db(
    session: AsyncSession, request_data: dict
) -> SummarizationResponse:
    """Saves a new summarization entry to the database cache, handling duplicates gracefully."""
    text_hash = request_data["text_hash"]
    mode = request_data["mode"]

    # Defensive measure: Ensure text_hash is always the canonical 16-character xxhash.
    # Log a warning if truncation occurs, indicating a potential issue upstream.
    if len(text_hash) > 16:
        logger.warning(
            f"Получен некорректный text_hash для сохранения: '{text_hash}'. "
            f"Усекаем до '{text_hash[:16]}' для согласованности."
        )
        text_hash = text_hash[:16]

    logger.debug(
        f"Попытка сохранения суммаризации в БД для hash={text_hash}, mode={mode}"
    )

    # Use ON CONFLICT DO NOTHING for idempotent insert
    insert_stmt = (
        insert(SummarizationCache)
        .values(
            text_hash=text_hash,
            original_text=request_data["original_text"],
            summary=request_data["summary"],
            mode=mode,
        )
        .on_conflict_do_nothing(index_elements=["text_hash", "mode"])
    )

    try:
        result = await session.execute(insert_stmt)
        await session.commit()

        if result.rowcount == 0:
            # No row was inserted, meaning it already existed due to a concurrent insert.
            logger.debug(
                f"Обнаружена конкурентная вставка или запись уже существует для hash={text_hash}, mode={mode}. "
                "Получение существующей записи."
            )
        else:
            logger.debug(
                f"Новая суммаризация сохранена для hash={text_hash}, mode={mode}"
            )

        # In either case (new insert or existing), retrieve the entry to return.
        final_summary = await get_summary_from_db(session, text_hash, mode)
        if final_summary:
            return final_summary
        else:
            # This case should ideally not happen if a UniqueViolationError was prevented by ON CONFLICT
            logger.error(
                "Не удалось получить суммаризацию после идемпотентной операции сохранения. Несогласованность данных."
            )
            raise RuntimeError(
                "Не удалось получить суммаризацию после операции сохранения."
            )

    except Exception as e:
        # Catch any other unexpected errors during the save process
        await session.rollback()  # Ensure transaction is rolled back on any exception
        logger.error(
            f"Произошла непредвиденная ошибка при сохранении суммаризации для hash={text_hash}, mode={mode}: {e}",
            exc_info=True,
        )
        raise RuntimeError(
            f"Произошла непредвиденная ошибка при сохранении суммаризации: {e}"
        )
