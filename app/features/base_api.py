# app/features/base_api.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from fastapi import APIRouter, Request, Response, status
from fastapi.responses import JSONResponse
from sqlmodel.ext.asyncio.session import AsyncSession
from arq.connections import ArqRedis
from loguru import logger

from app.features.tasks import (
    service as task_service,
    schemas as task_schemas,
    models as task_models,
)


class BaseAPIEndpoint(ABC):
    """
    Абстрактный базовый класс для создания стандартных API-эндпоинтов,
    которые работают по схеме: кэш -> фоновая задача.
    """

    # --- Эти атрибуты должны быть переопределены в дочерних классах ---
    ROUTER_PREFIX: str
    ROUTER_TAGS: list[str]
    TASK_TYPE: task_models.TaskType
    ARQ_TASK_NAME: str

    def __init__(self):
        self.router = APIRouter(prefix=self.ROUTER_PREFIX, tags=self.ROUTER_TAGS)
        self._register_routes()

    @abstractmethod
    def _get_task_id(self, request_data: Any) -> str:
        """Извлекает или генерирует уникальный ID задачи из данных запроса."""
        pass

    @abstractmethod
    async def _check_cache(
        self,
        request: Request,
        db_session: AsyncSession,
        task_id: str,
        request_data: Any,
    ) -> Optional[Dict[str, Any]]:
        """Проверяет наличие результата в кэше. Возвращает результат или None."""
        pass

    @abstractmethod
    def _get_arq_task_params(self, task_id: str, request_data: Any) -> Dict[str, Any]:
        """Формирует параметры для постановки задачи в очередь ARQ."""
        pass

    async def _handle_request(
        self,
        request: Request,
        response: Response,
        request_data: Any,
        db_session: AsyncSession,
        redis: ArqRedis,
    ):
        task_id = self._get_task_id(request_data)
        if not task_id:
            return JSONResponse(
                content={
                    "error": "Could not determine a valid Task ID from the request."
                },
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # 1. Проверка кэша
        cached_result = await self._check_cache(
            request, db_session, task_id, request_data
        )
        if cached_result:
            logger.info(f"Найден кэшированный результат для задачи {task_id}.")
            task = await task_service.create_task(db_session, self.TASK_TYPE, task_id)
            await task_service.store_task_result(db_session, task_id, cached_result)

            response_model = task_schemas.TaskStatusResponse(
                task_id=task_id,
                status=task_models.TaskStatus.COMPLETED,
                result_url=str(request.url_for("get_task_result", task_id=task_id)),
                sse_url=str(request.url_for("task_sse", task_id=task_id)),
            )
            return JSONResponse(
                content=response_model.model_dump(), status_code=status.HTTP_200_OK
            )

        # 2. Если в кэше нет, создаем задачу и ставим в очередь
        task = await task_service.create_task(db_session, self.TASK_TYPE, task_id)

        if task.status not in [
            task_models.TaskStatus.COMPLETED,
            task_models.TaskStatus.PROCESSING,
        ]:
            logger.info(
                f"Постановка задачи '{self.ARQ_TASK_NAME}' в очередь для task_id: {task_id}"
            )
            arq_params = self._get_arq_task_params(task_id, request_data)
            await redis.enqueue_job(self.ARQ_TASK_NAME, **arq_params)
        else:
            logger.debug(
                f"Задача {task_id} уже в статусе '{task.status}'. Новая задача не создается."
            )

        # 3. Возвращаем ответ 202 Accepted
        response_model = task_schemas.TaskCreateResponse(
            task_id=task_id,
            status_url=str(request.url_for("get_task_status", task_id=task_id)),
            sse_url=str(request.url_for("task_sse", task_id=task_id)),
        )
        return JSONResponse(
            content=response_model.model_dump(), status_code=status.HTTP_202_ACCEPTED
        )

    def _register_routes(self):
        # Этот метод будет использоваться для регистрации POST-эндпоинта.
        # Мы не можем определить его здесь полностью из-за зависимости от Pydantic модели запроса.
        # Реализация будет в дочерних классах.
        pass
