from pydantic import BaseModel, HttpUrl, model_validator
from datetime import datetime
from sqlmodel import SQLModel


class SubtitlesRequest(BaseModel):
    url: HttpUrl

    @model_validator(mode="after")
    def validate_youtube_url(self):
        if self.url:
            valid_domains = ["youtube.com", "www.youtube.com", "youtu.be"]
            if self.url.host not in valid_domains and not any(
                self.url.host.endswith(f".{domain}") for domain in valid_domains
            ):
                raise ValueError(
                    "URL must be a valid YouTube link (e.g., youtube.com, youtu.be)"
                )
        return self


class SubtitlesResponse(SQLModel):
    video_id: str
    title: str
    upload_date: datetime
    original_language: str
    ru_subtitles: str | None
    en_subtitles: str | None
