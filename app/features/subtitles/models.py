from sqlmodel import SQLModel, <PERSON>
from datetime import datetime


class SubtitlesCache(SQLModel, table=True):
    __tablename__ = "subtitles"
    video_id: str = Field(primary_key=True, max_length=20)
    title: str = Field(max_length=500)
    original_language: str = Field(max_length=10)
    upload_date: datetime
    ru_subtitles: str | None
    en_subtitles: str | None
    created_at: datetime = Field(default_factory=datetime.utcnow)
