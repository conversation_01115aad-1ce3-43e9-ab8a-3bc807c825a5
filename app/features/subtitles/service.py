from urllib.parse import urlparse, parse_qs, urlunparse, urlencode

from sqlmodel.ext.asyncio.session import AsyncSession
from sqlmodel import select
from loguru import logger

from .models import SubtitlesCache
from .schemas import SubtitlesResponse


def get_youtube_video_id_from_url(url: str) -> str | None:
    """Helper function to extract video_id, consolidating logic."""
    parsed_url = urlparse(url)
    if parsed_url.hostname in ("www.youtube.com", "youtube.com"):
        if parsed_url.path == "/watch":
            query_params = parse_qs(parsed_url.query)
            if "v" in query_params:
                return query_params["v"][0]
        elif parsed_url.path.startswith("/embed/") or parsed_url.path.startswith("/v/"):
            path_parts = parsed_url.path.split("/")
            if len(path_parts) > 2:
                return path_parts[2]
        elif parsed_url.path.startswith("/shorts/"):
            path_parts = parsed_url.path.split("/")
            if len(path_parts) > 2:
                return path_parts[2]
    elif parsed_url.hostname in ("youtu.be",):
        path_parts = parsed_url.path.split("/")
        if len(path_parts) > 1:
            return path_parts[1]
    return None


def clean_youtube_url(url: str) -> str:
    """
    Очищает URL YouTube видео, приводя его к каноническому виду
    https://www.youtube.com/watch?v=VIDEO_ID.
    Если video_id не может быть извлечен, возвращает оригинальный URL.
    """
    video_id = get_youtube_video_id_from_url(url)
    if video_id:
        return urlunparse(
            ("https", "www.youtube.com", "/watch", "", urlencode({"v": video_id}), "")
        )
    return url


def extract_video_id(url: str) -> str | None:
    """Извлекает video_id из различных форматов URL YouTube."""
    video_id = get_youtube_video_id_from_url(url)
    if not video_id:
        logger.warning(f"Не удалось извлечь video_id из URL: {url}")
    return video_id


async def get_subtitles_from_db(
    session: AsyncSession, video_id: str
) -> SubtitlesResponse | None:
    """
    Пытается получить кэшированные субтитры из базы данных по video_id.
    """
    logger.debug(f"Проверка кэша для video_id: {video_id}")
    result = await session.execute(
        select(SubtitlesCache).where(SubtitlesCache.video_id == video_id)
    )
    cached_subtitles = result.scalars().first()
    if cached_subtitles:
        logger.debug(f"Найдено в кэше для video_id: {video_id}")
        return SubtitlesResponse.model_validate(cached_subtitles)
    logger.debug(f"Не найдено в кэше для video_id: {video_id}")
    return None


async def save_subtitles_to_db(session: AsyncSession, data: dict) -> SubtitlesResponse:
    """
    Сохраняет или обновляет субтитры в базе данных.
    """
    video_id = data.get("video_id")
    if not video_id:
        logger.error("Попытка сохранить субтитры без video_id.")
        raise ValueError("Для сохранения субтитров необходим Video ID.")

    # Проверяем, существует ли уже запись
    result = await session.execute(
        select(SubtitlesCache).where(SubtitlesCache.video_id == video_id)
    )
    existing_entry = result.scalars().first()

    if existing_entry:
        logger.debug(f"Обновление существующей записи для video_id: {video_id}")
        for key, value in data.items():
            setattr(existing_entry, key, value)
        session.add(existing_entry)
        await session.commit()
        await session.refresh(existing_entry)
        return SubtitlesResponse.model_validate(existing_entry)
    else:
        logger.debug(f"Сохранение новой записи для video_id: {video_id}")
        # Создаем новый объект SubtitlesCache из словаря данных
        # Убедимся, что данные соответствуют модели
        new_cache_entry = SubtitlesCache(**data)
        session.add(new_cache_entry)
        await session.commit()
        await session.refresh(new_cache_entry)
        return SubtitlesResponse.model_validate(new_cache_entry)
