from typing import Dict, Any, Optional
from sqlmodel.ext.asyncio.session import AsyncSession
from fastapi import Depends, Request, Response
from arq.connections import ArqRedis

from app.features.base_api import BaseAPIEndpoint
from app.features.tasks.models import TaskType
from app.core.db import get_db_session, get_redis_pool
from . import schemas, service


class SubtitlesAPIEndpoint(BaseAPIEndpoint):
    ROUTER_PREFIX = "/subtitles"
    ROUTER_TAGS = ["Subtitles"]
    TASK_TYPE = TaskType.SUBTITLES
    ARQ_TASK_NAME = "download_subtitles"

    def _get_task_id(self, request_data: schemas.SubtitlesRequest) -> str:
        cleaned_url = service.clean_youtube_url(str(request_data.url))
        return service.extract_video_id(cleaned_url)

    async def _check_cache(
        self,
        request: Request,
        db_session: AsyncSession,
        task_id: str,
        request_data: schemas.SubtitlesRequest,
    ) -> Optional[Dict[str, Any]]:
        cached = await service.get_subtitles_from_db(db_session, task_id)
        if cached:
            # Важно! Даты нужно конвертировать в строку для JSON
            cached_data = cached.model_dump()
            if cached_data.get("upload_date"):
                cached_data["upload_date"] = cached_data["upload_date"].isoformat()
            return cached_data
        return None

    def _get_arq_task_params(
        self, task_id: str, request_data: schemas.SubtitlesRequest
    ) -> Dict[str, Any]:
        cleaned_url = service.clean_youtube_url(str(request_data.url))
        return {"video_url": cleaned_url, "task_id": task_id}

    def _register_routes(self):
        @self.router.post(
            "/", response_model=None, status_code=202
        )  # Модель ответа управляется базовым классом
        async def get_subtitles_endpoint(
            request: Request,
            response: Response,
            request_data: schemas.SubtitlesRequest,
            db_session: AsyncSession = Depends(get_db_session),
            redis: ArqRedis = Depends(get_redis_pool),
        ):
            return await self._handle_request(
                request, response, request_data, db_session, redis
            )


# Создаем экземпляр и экспортируем роутер
endpoint = SubtitlesAPIEndpoint()
router = endpoint.router
