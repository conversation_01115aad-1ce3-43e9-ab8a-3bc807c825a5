import sys
import logging
from loguru import logger
from pathlib import Path


class InterceptHandler(logging.Handler):
    """
    Intercepts standard Python logging messages and redirects them to Loguru.
    """

    def emit(self, record):
        # Get corresponding Loguru level
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelname

        # Find caller from where the log message originated
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        message = record.getMessage()
        if len(message) > 500:
            message = message[:500] + "... (обрезано до 500 символов...)"

        logger.opt(depth=depth, exception=record.exc_info).log(level, message)


def configure_logging(log_type: str):
    setup_loguru_logging(log_level="DEBUG", use_json=False, log_type=log_type)


def setup_loguru_logging(
    log_level: str = "INFO", use_json: bool = False, log_type: str = ""
):
    """
    Setup loguru logging for the application.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        use_json: Use JSON format for structured output
        log_type: Type of log (e.g., "MANAGER", "API", "WORKER") to include in the format
    """
    # Remove default handler
    logger.remove()

    # Ensure log level is uppercase
    log_level = log_level.upper()
    debug = log_level == "DEBUG"

    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Configure specific loggers that might be noisy or need custom levels
    loggers_to_intercept = [
        "uvicorn",
        "uvicorn.access",
        "uvicorn.error",
        "websockets",
        "websockets.server",
        "websockets.protocol",
        "sqlalchemy",
        "arq",
        "httpx",
        "openai",
    ]
    for logger_name in loggers_to_intercept:
        _logger = logging.getLogger(logger_name)
        _logger.handlers = [InterceptHandler()]
        _logger.propagate = (
            False  # Prevent logs from going to root logger if already handled
        )
        if logger_name in [
            "sqlalchemy",
            "arq",
            "httpx",
            "openai",
        ]:  # Set warning level for these
            _logger.setLevel(logging.WARNING)

    # Add file handler with structured format
    log_file = Path(__file__).parent.parent.parent / "app.log"
    logger.add(
        log_file,
        rotation="10 MB",
        retention="1 week",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        backtrace=True,
        diagnose=True,
        serialize=False,  # Keep human-readable format for file logs
    )

    # Add stdout handler with enhanced formatting
    if debug:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            f"[<fg #999999>{log_type}</fg #999999>] <level>{{message}}</level>"
            if log_type
            else "<level>{{message}}</level>"
        )
    else:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan> | "
            f"[<fg #999999>{log_type}</fg #999999>] <level>{{message}}</level>"
            if log_type
            else "<level>{{message}}</level>"
        )

    # Add JSON format option for structured output
    if use_json:
        # For JSON, log_type could be added as an 'extra' field or integrated into message
        # For now, keeping it simple without special handling for log_type in JSON format string
        format_string = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message} | {extra}"

    # Filter function to reduce noise from websockets ping/pong
    def filter_noisy_logs(record):
        message = record["message"].lower()
        # Filter out websockets ping/pong messages
        if any(
            keyword in message
            for keyword in [
                "keepalive ping",
                "keepalive pong",
                "sending ping",
                "received pong",
            ]
        ):
            return False
        # Filter out uvicorn access logs in non-debug mode
        if not debug and record["name"] == "uvicorn.access":
            return False
        return True

    logger.add(
        sys.stdout,
        colorize=not use_json,
        level=log_level,
        format=format_string,
        filter=filter_noisy_logs,
        serialize=use_json,
    )
