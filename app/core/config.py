from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    DATABASE_URL: str
    REDIS_URL: str
    GEMINI_API_KEY: str
    OPENROUTER_API_KEY: str | None = None
    SOCKS5_PROXY_GEMINI: str | None = None
    SOCKS5_PROXY_YOUTUBE: str | None = None
    YOUTUBE_COOKIES_FILE: str | None = None
    SUBTITLE_FORMAT: str = "ttml"  # "json3" or "ttml"

    # TTS settings
    EDGE_TTS_VOICE: str = "ru-RU-SvetlanaNeural"
    EDGE_TTS_RATE: str = "+40%"
    GEMINI_TTS_VOICE: str = "Kore"

    # MinIO S3 settings
    MINIO_ENDPOINT: str
    MINIO_PORT: int = 9000
    MINIO_ACCESS_KEY: str
    MINIO_SECRET_KEY: str
    MINIO_BUCKET_NAME: str = "audio-files"
    MINIO_USE_SSL: bool = False

    # CORS settings (максимально открытые значения по умолчанию)
    CORS_ORIGINS: str = "*"  # Comma-separated list of allowed origins. Use '*' for development only! Example: 'https://yourdomain.com,https://app.yourdomain.com'",
    CORS_ALLOW_CREDENTIALS: bool = True  # Allow credentials in CORS requests
    CORS_ALLOW_METHODS: str = (
        "GET,POST,PUT,DELETE,OPTIONS"  # Comma-separated list of allowed HTTP methods"
    )
    CORS_ALLOW_HEADERS: str = "Content-Type,Authorization,X-API-Key"  # Comma-separated list of allowed headers
    # CORS_ORIGINS: str = "*"
    # CORS_ALLOW_METHODS: str = "*"
    # CORS_ALLOW_HEADERS: str = "*"
    CORS_ALLOW_CREDENTIALS: bool = True

    GOOGLE_CLOUD_PROJECT: str | None = None  # for dev gem-cli

    class Config:
        env_file = ".env"


settings = Settings()

# def get_settings() -> Settings:
#     """Get application settings singleton."""
#     return Settings()
