class WorkerFatalError(Exception):
    """Сигнализирует о неисправимой ошибке, требующей перезапуска воркера."""

    pass


class WorkerTransientError(Exception):
    """Сигнализирует о временной ошибке, допускающей повторную попытку."""

    pass


class SubtitlesNotFoundError(Exception):
    """
    Возбуждается, когда субтитры для целевых языков не найдены
    ни одной из доступных стратегий.
    """

    pass


class YouTubeAudioProcessingError(Exception):
    """Raised when YouTube audio processing (e.g., FFmpeg) fails."""

    pass


class YouTubeAudioDownloadError(Exception):
    """Raised when YouTube audio download fails."""

    pass


class AIClientError(Exception):
    """Base exception for AI client errors."""

    pass


class AIAPIError(Exception):
    """Raised when there's an error from the AI API itself."""

    pass


class NetworkError(Exception):
    """Raised for network-related errors during AI API calls."""

    pass


class AIResponseError(AIClientError):
    """
    Возбуждается, когда AI модель вернула пустой, заблокированный
    или некорректный ответ.
    """

    pass
