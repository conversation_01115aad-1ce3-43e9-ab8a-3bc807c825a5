from sqlmodel.ext.asyncio.session import AsyncSession
from sqlalchemy.orm import sessionmaker
from arq import create_pool
from arq.connections import RedisSettings
from sqlalchemy.ext.asyncio import create_async_engine

from .config import settings

# Настройка PostgreSQL
# Настройка PostgreSQL с явным connection pooling
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,
    pool_size=10,  # количество постоянных соединений
    max_overflow=20,  # сколько дополнительных соединений можно создать при пике
    pool_recycle=1800,  # время жизни соединения (секунды)
)
AsyncSessionFactory = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


async def get_db_session() -> AsyncSession:
    async with AsyncSessionFactory() as session:
        yield session


# Настройка Redis для ARQ
redis_settings = RedisSettings.from_dsn(settings.REDIS_URL)


async def get_redis_pool():
    return await create_pool(redis_settings)
