from fastapi import FastAP<PERSON>, Request
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_ipaddr
from slowapi.errors import RateLimitExceeded
from loguru import logger
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings

# Инициализируем Limiter
limiter = Limiter(key_func=get_ipaddr, default_limits=["1/second"])


def setup_cors(app: FastAPI):
    """
    Configures CORS middleware for the FastAPI application using settings.
    By default, allows all origins, methods, and headers (for development).
    """
    origins = [
        origin.strip() for origin in settings.CORS_ORIGINS.split(",") if origin.strip()
    ]
    if settings.CORS_ORIGINS == "*":
        origins = ["*"]
        logger.warning(
            "\u26a0\ufe0f CORS configured to allow ALL origins (*) - use caution in production!"
        )
    methods = [m.strip() for m in settings.CORS_ALLOW_METHODS.split(",") if m.strip()]
    if settings.CORS_ALLOW_METHODS == "*":
        methods = ["*"]
    headers = [h.strip() for h in settings.CORS_ALLOW_HEADERS.split(",") if h.strip()]
    if settings.CORS_ALLOW_HEADERS == "*":
        headers = ["*"]
    logger.info(f"\u2705 CORS origins: {origins}")
    logger.info(f"\u2705 CORS methods: {methods}")
    logger.info(f"\u2705 CORS headers: {headers}")
    logger.info(f"\u2705 CORS credentials: {settings.CORS_ALLOW_CREDENTIALS}")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        # allow_methods=["*"], # Проще всего разрешить все
        # allow_headers=["*"], # И все заголовки
        allow_methods=methods,
        allow_headers=headers,
    )
    logger.info("\u2705 CORS middleware configured successfully")


def setup_rate_limiter(app: FastAPI):
    """
    Configures SlowAPI rate limiting for the FastAPI application.
    """
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

    @app.exception_handler(RateLimitExceeded)
    async def custom_rate_limit_exceeded_handler(
        request: Request, exc: RateLimitExceeded
    ):
        logger.warning(
            f"Rate limit exceeded for IP: {get_ipaddr(request)} on path: {request.url.path}. Details: {exc.detail}"
        )
        # Call the default handler to ensure proper headers are set
        return _rate_limit_exceeded_handler(request, exc)
