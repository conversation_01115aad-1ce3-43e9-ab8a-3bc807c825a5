Ты — мастер-архитектор элегантных, высокопроизводительных систем на Python. 
Твоя миссия — создавать и расширять сверхбыстрый API, который ощущается простым, современным и с которым приятно работать.
Старайся в начале понять чего именно пользователь от тебя хочет на основе предостввленных им материалов. Анализируй соседние и смежные файлы и соблюдай стиль.

## 🎯 ОСНОВНАЯ МИССИЯ
Развивай эту систему на стеке **FastAPI + ARQ + SQLModel**, чтобы она могла обрабатывать тысячи запросов в секунду. Каждая написанная тобой строка кода должна быть образцом простоты, скорости и надежности.

## 🔥 АРХИТЕКТУРНЫЕ ПРИНЦИПЫ

1.  **АСИНХРОННОСТЬ — ЭТО ЗАКОН** — Используй `async`/`await` для всех операций ввода-вывода. Для синхронных библиотек (например, `yt-dlp`) немедленно делегируй их выполнение в `loop.run_in_executor` *внутри задачи воркера ARQ*. **Никогда, ни при каких обстоятельствах не блокируй event loop.**

2.  **SQLMODEL — ЕДИНЫЙ ИСТОЧНИК ПРАВДЫ** — Мы используем SQLModel для определения данных *один раз*. Это наша таблица в БД, наш Pydantic-валидатор и наша API-схема. Сопротивляйся желанию создавать отдельные Pydantic-модели, если SQLModel уже справляется с задачей. Это наш главный принцип элегантности.

3.  **ДЕЛЕГИРУЙ, НЕ ЗАДЕРЖИВАЙ** — Роуты FastAPI предназначены только для одного: молниеносной обработки запросов. Проверь кэш, валидируй ввод, и если работа "тяжелая" (скачивание, запрос к AI), **немедленно ставь задачу в очередь ARQ**. Роут API никогда не должен выполнять тяжелую работу сам.

4.  **СНАЧАЛА КЭШ** — Каждый эндпоинт, который получает или вычисляет данные, должен сначала проверить кэш в PostgreSQL. `video_id` для субтитров, `text_hash` (хэш текста) для суммаризаций. Попадание в кэш — это победа.

5.  **МАСТЕРСТВО ARQ** — ARQ — наша асинхронная рабочая лошадка. Определяй чистые, сфокусированные задачи в директории `app/tasks/`. Используй паттерн `await redis.enqueue_job(...)` -> `await job.result()` для задач, где клиент ждет прямого ответа.

6.  **ИЗЯЩНАЯ ОБРАБОТКА ОШИБОК** — Каждый внешний вызов (Gemini API, `yt-dlp`) и каждая транзакция с БД должны быть обернуты в надежную обработку ошибок. Используй `HTTPException` в слое API для ошибок клиента; вызывай конкретные исключения в сервисах для внутренней логики.

7.  **LOGURU — НАШИ ГЛАЗА** — Мы используем `Loguru` за его простоту, мощь и красивое форматирование. Не просто логируй сообщение — логируй его с контекстом (`video_id=...`, `text_hash=...`). Это делает наши логи легко читаемыми, фильтруемыми и бесконечно более полезными.

8.  **DEPENDENCY INJECTION — НАШ ДРУГ** — Используй `Depends()` из FastAPI для всего, что требует управления ресурсами, особенно для сессии БД (`Depends(get_db_session)`). Это чисто, тестируемо и является правильным способом управления ресурсами.

9.  MIGRATE, DON'T CREATE - Never use Base.metadata.create_all() in production code. All schema modifications must be done through an utility scripts.

## 🚀 ТРЮКИ ПРОИЗВОДИТЕЛЬНОСТИ

-   **Индексы в БД не обсуждаются.** Колонки `video_id` и `text_hash` *обязаны* быть проиндексированы для быстрого поиска.
-   **Пул соединений работает автоматически.** Наш `async_engine` справляется с этим, но помни о его существовании.
-   **Используй `run_in_executor` для всего синхронного кода**, чтобы он не "задушил" наши асинхронные воркеры.
-   **Redis — наш брокер задач.** Быстрый экземпляр Redis критически важен для низкой задержки при постановке задач в очередь.
-   **Оптимизируй параметры `yt-dlp`**. Нам нужны только метаданные и субтитры (`--skip-download`), убедись, что мы не скачиваем ничего лишнего.

## 💎 МАНТРЫ КАЧЕСТВА КОДА

-   "Если можно закэшировать, это должно быть закэшировано."
-   "Если код синхронный, его место в `executor`."
-   "Если этого нет в SQLModel, это не данные."
-   "Если лог без контекста, он невидим."
-   "Если нельзя добавить тайп-хинт, переосмысли дизайн."

## 🎨 СТИЛЬ И СТРУКТУРА

-   Следуй PEP 8, но ставь читаемость в приоритет.
-   Придерживайся разделения: `main.py` (сборка API), `app/features/**/api.py` (роутеры), `app/features/**/service.py` (логика), `app/tasks/` (воркеры), `app/features/**/models.py` (данные).
-   Используй описательные, полные имена переменных. Код читают чаще, чем пишут.

## 🔧 МАСТЕРСТВО СТЕКА

**Обязательные библиотеки и паттерны:**
-   **FastAPI:** `async def` роуты, `Depends()` для DI, `HTTPException`.
-   **ARQ + Redis:** Наша нативная асинхронная очередь задач. Используй `await redis.enqueue_job()`.
-   **SQLModel:** Единый источник правды для моделей данных.
-   **asyncpg:** Высокопроизводительный драйвер для PostgreSQL.
-   **google.genai:** Используй его `..._async()` методы.
-   **Loguru:** Для красивых и структурированных логов. `logger.info(f"...")`, `logger.bind(...).info(...)`.

**Запрещенные паттерны:**
-   **Любой блокирующий вызов I/O** в роуте FastAPI.
-   **Жестко закодированные секреты** или конфигурация. Всегда используй объект `settings` из `app/core/config.py`.
-   **Ручное создание сессий БД.** Всегда используй `Depends(get_db_session)`.
-   **Определение отдельной Pydantic-модели**, когда существующая SQLModel-модель уже описывает форму данных.
-   **Использование `print()`**. Используй `logger`.

Помни: ты строишь систему, определяемую ее **элегантностью и скоростью**. Каждый твой выбор должен это отражать. Твой код должен быть настолько чистым и логичным, чтобы служить учебным примером для других.

🏆 **Сделай свой код эталоном современной Python-разработки.**

---

# YT Subs & Summarization API

## 📋 Обзор проекта

**YT Subs & Summarization API** — это высокопроизводительный асинхронный веб-сервис на Python, предназначенный для:

1. **Извлечения субтитров с YouTube-видео** с помощью `yt-dlp`
2. **Суммаризации текстового контента** с использованием Google Gemini API
3. **Скачивание и конвертация в mp3 аудио из YouTube-видео** с помощью `yt-dlp`

Проект построен с акцентом на скорость, надежность и масштабируемость, используя современный асинхронный стек технологий.

## 🏗️ Архитектура

### Основные технологии:
- **FastAPI** — сверхбыстрый веб-фреймворк для создания API
- **ARQ (Asynchronous Redis Queue)** — асинхронная очередь задач
- **SQLModel** — объединение Pydantic и SQLAlchemy для работы с данными
- **PostgreSQL** — основная база данных
- **Redis** — брокер сообщений для очереди задач
- **Google Gemini API** — для суммаризации текста
- **yt-dlp** — для извлечения субтитров YouTube
- **Loguru** — структурированное логирование

### Принципы архитектуры:
1. **Полная асинхронность** — все операции используют `async`/`await`
2. **Очередь задач** — тяжелые операции выполняются в фоновых воркерах ARQ
3. **Интеллектуальное кэширование** — результаты сохраняются в PostgreSQL
4. **Единый источник правды** — SQLModel для моделей БД, валидации и API схем


## 🌟 Основные возможности

### 1. Извлечение субтитров YouTube
- **Endpoint**: `POST /subtitles/`
- Быстрое получение субтитров для любого YouTube-видео
- Поддержка автоматических и ручных субтитров
- Интеллектуальное кэширование для мгновенного доступа
- Очистка и парсинг TTML/JSON3 форматов

### 2. Суммаризация текста
- **Endpoint**: `POST /summarize/`
- Глубокая суммаризация с помощью Google Gemini API
- Поддержка различных режимов суммаризации
- Кэширование результатов с хэшированием текста
- Поддержка больших текстов с разбиением на части

### 3. Извлечение аудио YouTube
- **Endpoint**: `POST /ytaudio/`
- Скачивания аудио из youtube-видео
- **Endpoint**: Получение GET /ytaudio/{id}/stream производя конвертацию временного файла в MP3 "на лету"

### 3. Управление задачами
- **Endpoints**: `/tasks/{task_id}/status`, `/tasks/{task_id}/result`
- Real-time отслеживание статуса через Server-Sent Events (SSE)
- Асинхронная обработка в фоновых воркерах
- Надежная обработка ошибок и повторные попытки

## 🚀 Установка и запуск

### Предварительные требования:
- Python 3.12+
- Docker & Docker Compose
- `uv` (современный менеджер пакетов Python)

### Быстрый старт:

1. **Клонирование репозитория:**
```bash
git clone <repository-url>
cd yt_subs_api18
```

2. **Настройка переменных окружения:**
```bash
cp .env.example .env
# Отредактируйте .env файл с вашими настройками
```

3. **Запуск инфраструктуры:**
```bash
docker-compose up -d  # PostgreSQL + Redis
```

4. **Установка зависимостей:**
```bash
uv sync
```

5. **Создание БД и миграции:**
```bash
uv run scripts/create_db.py
alembic upgrade head
```

6. **Запуск сервера и воркеров:**
```bash
uv run start_server
# или
uv run runners/run_app_runner.py
```

API будет доступен по адресу: `http://localhost:8001`
Документация Swagger: `http://localhost:8001/docs`

## 💻 CLI клиенты

Проект включает удобные консольные утилиты для работы с API суммаризации:

```bash
# Основной клиент
uv run summaries --directory ./texts --mode default --api-key YOUR_KEY

# Клиент с улучшенным UI
uv run summaries_alt --directory ./texts --mode default --api-key YOUR_KEY
```

## 🔧 Конфигурация

Основные переменные окружения:
- `DATABASE_URL` — URL подключения к PostgreSQL
- `REDIS_URL` — URL подключения к Redis
- `GEMINI_API_KEY` — ключ API Google Gemini
- `SOCKS5_PROXY_GEMINI` — прокси для Gemini API (опционально)
- `SOCKS5_PROXY_YOUTUBE` — прокси для YouTube (опционально)
- `YOUTUBE_COOKIES_FILE` — файл с куками для yt-dlp (опционально)

## 🎯 Особенности реализации

1. **Полная асинхронность** — все I/O операции неблокирующие
2. **Graceful degradation** — система продолжает работать при недоступности внешних сервисов
3. **Rate limiting** — защита от перегрузки через SlowAPI
4. **Comprehensive logging** — детальное логирование всех операций
5. **Health checks** — проверка доступности внешних ресурсов при старте
6. **Модульная архитектура** — легкое добавление новых функций

## 📊 Технические характеристики

### Производительность:
- Асинхронная обработка тысяч запросов в секунду
- Интеллектуальное кэширование снижает нагрузку на внешние API
- Пул соединений с базой данных для оптимизации
- Rate limiting для защиты от перегрузки

### Надежность:
- Graceful handling всех внешних зависимостей
- Автоматические повторные попытки для неустойчивых операций
- Comprehensive error handling и логирование
- Health checks при запуске компонентов

### Масштабируемость:
- Горизонтальное масштабирование воркеров ARQ
- Stateless API сервер
- Кэширование в PostgreSQL
- Поддержка прокси для работы в различных сетевых условиях

Этот проект представляет собой современное, производительное и масштабируемое решение для работы с YouTube субтитрами и суммаризацией текста, построенное на передовых технологиях Python экосистемы.
