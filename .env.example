DATABASE_URL=postgresql+asyncpg://user:password@localhost/app_db
REDIS_URL=redis://localhost:6379
GEMINI_API_KEY=ВАШ_КЛЮЧ_GEMINI_API
OPENROUTER_API_KEY=ВАШ_КЛЮЧ_OPENROUTER_API
# SOCKS5_PROXY_GEMINI=socks5://127.0.0.1:1080
# SOCKS5_PROXY_YOUTUBE=socks5://************:1080
#YOUTUBE_COOKIES_FILE=/tmp/youtube_getcookies.txt

# CORS Settings - SECURITY CRITICAL!
# ⚠️  WARNING: Using "*" allows ALL domains - SECURITY RISK in production!
# ✅ PRODUCTION: Set specific domains like: https://yourdomain.com,https://app.yourdomain.com
CORS_ORIGINS=*
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Authorization,Content-Type,X-API-Key
CORS_ALLOW_CREDENTIALS=true

SUBTITLE_FORMAT=json3

GOOGLE_CLOUD_PROJECT=

# MinIO S3 settings
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=audio-files
MINIO_USE_SSL=false
