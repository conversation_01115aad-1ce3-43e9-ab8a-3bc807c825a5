import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool
from sqlmodel import SQLModel

from alembic import context
from loguru import logger

# Add the project root directory to the Python path
# This is necessary for Alembic to find the app's modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import your models here so that they are registered with SQLModel's metadata
from app.features.tasks.models import Task  # noqa
from app.features.subtitles.models import SubtitlesCache  # noqa
from app.features.summarization.models import SummarizationCache  # noqa
from app.features.youtube_audio.models import YouTubeAudio  # noqa
from app.features.tts.models import TTS  # noqa

# Import the application settings
from app.core.config import settings

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Set the database URL from the application settings
# This overrides the sqlalchemy.url in alembic.ini
# Alembic migration generation requires a synchronous database driver.
# We replace the 'asyncpg' driver specified in the DATABASE_URL with a
# synchronous one (psycopg2) for Alembic's operations.
sync_db_url = settings.DATABASE_URL.replace("+asyncpg", "")
logger.info(f"Alembic will use database URL: {sync_db_url}")
config.set_main_option("sqlalchemy.url", sync_db_url)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the target metadata for 'autogenerate' support
target_metadata = SQLModel.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
