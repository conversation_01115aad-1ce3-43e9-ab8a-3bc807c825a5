"""Remove lowercase youtube_audio from TaskType enum and update existing rows

Revision ID: d63bbf6bc8d3
Revises: 819ae99455b3
Create Date: 2025-07-01 10:03:57.348637

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "d63bbf6bc8d3"
down_revision: Union[str, Sequence[str], None] = "819ae99455b3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Update existing 'task_type' values from 'youtube_audio' to 'YOUTUBE_AUDIO'
    op.execute(
        "UPDATE task SET task_type = 'YOUTUBE_AUDIO' WHERE task_type = 'youtube_audio'"
    )

    # Recreate the enum type to remove 'youtube_audio'
    op.execute("ALTER TABLE task ALTER COLUMN task_type DROP NOT NULL")
    op.execute("ALTER TYPE tasktype RENAME TO tasktype_old")
    sa.Enum(
        "SUBTITLES", "SUMMARIZATION", "YOUTUBE_AUDIO", "TTS", name="tasktype"
    ).create(op.get_bind())
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE tasktype USING task_type::text::tasktype"
    )
    op.execute("DROP TYPE tasktype_old")
    op.execute("ALTER TABLE task ALTER COLUMN task_type SET NOT NULL")
    op.drop_index(op.f("ix_tts_text_hash"), table_name="tts")
    op.create_index(op.f("ix_tts_text_hash"), "tts", ["text_hash"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Recreate the enum type to include 'youtube_audio' again for downgrade
    op.execute("ALTER TABLE task ALTER COLUMN task_type DROP NOT NULL")
    op.execute("ALTER TYPE tasktype RENAME TO tasktype_new")
    sa.Enum(
        "SUBTITLES",
        "SUMMARIZATION",
        "youtube_audio",
        "YOUTUBE_AUDIO",
        "TTS",
        name="tasktype",
    ).create(op.get_bind())
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE tasktype USING task_type::text::tasktype"
    )
    op.execute("DROP TYPE tasktype_new")
    op.execute("ALTER TABLE task ALTER COLUMN task_type SET NOT NULL")
    op.drop_index(op.f("ix_tts_text_hash"), table_name="tts")
    op.create_index(op.f("ix_tts_text_hash"), "tts", ["text_hash"], unique=True)
    # ### end Alembic commands ###
