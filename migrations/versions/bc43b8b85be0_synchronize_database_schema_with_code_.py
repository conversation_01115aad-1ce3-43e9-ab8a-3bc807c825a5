"""Synchronize database schema with code definitions

Revision ID: bc43b8b85be0
Revises: e2fb71b2359c
Create Date: 2025-07-01 10:08:53.917241

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "bc43b8b85be0"
down_revision: Union[str, Sequence[str], None] = "e2fb71b2359c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
