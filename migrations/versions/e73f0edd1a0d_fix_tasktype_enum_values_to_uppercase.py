"""Fix TaskType enum values to uppercase

Revision ID: e73f0edd1a0d
Revises: 5b8cd26f817f
Create Date: 2025-06-25 20:10:48.577801

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "e73f0edd1a0d"
down_revision: Union[str, Sequence[str], None] = "5b8cd26f817f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add new enum value with correct case
    op.execute("ALTER TYPE tasktype ADD VALUE 'YOUTUBE_AUDIO'")


def downgrade() -> None:
    """Downgrade schema."""
    pass
