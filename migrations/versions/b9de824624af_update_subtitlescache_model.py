"""Update SubtitlesCache model

Revision ID: b9de824624af
Revises: 69729f1b0fb3
Create Date: 2025-06-21 10:58:45.213640

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "b9de824624af"
down_revision: Union[str, Sequence[str], None] = "69729f1b0fb3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_summarizationcache_mode"), table_name="summaries")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        op.f("ix_summarizationcache_mode"), "summaries", ["mode"], unique=False
    )
    # ### end Alembic commands ###
