"""Add tts table after model import

Revision ID: b78ec83c0465
Revises: 1cc88f62f47c
Create Date: 2025-07-01 09:33:07.618795

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b78ec83c0465"
down_revision: Union[str, Sequence[str], None] = "1cc88f62f47c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "tts",
        sa.Column("text", sa.String(), nullable=False),
        sa.Column("engine", sa.String(), nullable=False),
        sa.Column("audio_file_link", sa.String(), nullable=True),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("task_id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_tts_task_id"), "tts", ["task_id"], unique=True)
    op.create_index(op.f("ix_tts_text"), "tts", ["text"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_tts_text"), table_name="tts")
    op.drop_index(op.f("ix_tts_task_id"), table_name="tts")
    op.drop_table("tts")
    # ### end Alembic commands ###
