"""Add file_name to TTS model

Revision ID: b7395de96985
Revises: fac7a46807ae
Create Date: 2025-07-01 11:02:54.311260

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = "b7395de96985"
down_revision: Union[str, Sequence[str], None] = "fac7a46807ae"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tts", sa.Column("file_name", sqlmodel.sql.sqltypes.AutoString(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tts", "file_name")
    # ### end Alembic commands ###
