"""Update tts table with text_hash and file details

Revision ID: 6d830a3bf192
Revises: b78ec83c0465
Create Date: 2025-07-01 09:37:44.128247

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "6d830a3bf192"
down_revision: Union[str, Sequence[str], None] = "b78ec83c0465"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tts", sa.Column("file_path", sa.String(), nullable=True))
    op.add_column("tts", sa.Column("file_name", sa.String(), nullable=True))
    op.add_column("tts", sa.Column("file_size_bytes", sa.Integer(), nullable=True))
    op.drop_index(op.f("ix_tts_text"), table_name="tts")
    op.drop_column("tts", "audio_file_link")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tts",
        sa.Column("audio_file_link", sa.String(), autoincrement=False, nullable=True),
    )
    op.create_index(op.f("ix_tts_text"), "tts", ["text"], unique=False)
    op.drop_column("tts", "file_size_bytes")
    op.drop_column("tts", "file_name")
    op.drop_column("tts", "file_path")
    # ### end Alembic commands ###
