"""Add S3 URL fields to TTS and YouTubeAudio models

Revision ID: c6b4e9d5b36f
Revises: c4da79d7c755
Create Date: 2025-07-01 13:01:22.137555

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "c6b4e9d5b36f"
down_revision: Union[str, Sequence[str], None] = "c4da79d7c755"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add s3_url column to tts table
    op.add_column("tts", sa.Column("s3_url", sa.String(), nullable=True))

    # Add s3_url_original and s3_url_mp3 columns to youtubeaudio table
    op.add_column(
        "youtubeaudio", sa.Column("s3_url_original", sa.String(), nullable=True)
    )
    op.add_column("youtubeaudio", sa.Column("s3_url_mp3", sa.String(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove s3_url columns
    op.drop_column("youtubeaudio", "s3_url_mp3")
    op.drop_column("youtubeaudio", "s3_url_original")
    op.drop_column("tts", "s3_url")
