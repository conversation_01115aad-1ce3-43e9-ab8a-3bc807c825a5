"""Recreate YouTubeAudio table if missing

Revision ID: dc4858629ab8
Revises: 3be1321f525a
Create Date: 2025-06-25 19:21:23.928490

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "dc4858629ab8"
down_revision: Union[str, Sequence[str], None] = "3be1321f525a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
