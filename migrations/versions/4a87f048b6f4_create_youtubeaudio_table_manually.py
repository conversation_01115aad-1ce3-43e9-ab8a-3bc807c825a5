"""Create YouTubeAudio table manually

Revision ID: 4a87f048b6f4
Revises: dc4858629ab8
Create Date: 2025-06-25 19:35:00.220938

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "4a87f048b6f4"
down_revision: Union[str, Sequence[str], None] = "dc4858629ab8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "youtubeaudio",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("video_id", sa.String(), nullable=False),
        sa.Column("file_path", sa.String(), nullable=False),
        sa.Column("file_name", sa.String(), nullable=False),
        sa.Column("file_size_bytes", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("video_id"),
    )
    op.create_index(
        op.f("ix_youtubeaudio_video_id"), "youtubeaudio", ["video_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_youtubeaudio_video_id"), table_name="youtubeaudio")
    op.drop_table("youtubeaudio")
    # ### end Alembic commands ###
