"""Add YOUTUBE_AUDIO to TaskType enum

Revision ID: 5b8cd26f817f
Revises: 4a87f048b6f4
Create Date: 2025-06-25 20:07:06.311974

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "5b8cd26f817f"
down_revision: Union[str, Sequence[str], None] = "4a87f048b6f4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add new enum value to existing TaskType enum
    op.execute("ALTER TYPE tasktype ADD VALUE 'youtube_audio'")


def downgrade() -> None:
    """Downgrade schema."""
    pass
