"""Fix TTS primary key to composite text_hash and engine

Revision ID: c4da79d7c755
Revises: 60146069d841
Create Date: 2025-07-01 12:31:01.035059

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "c4da79d7c755"
down_revision: Union[str, Sequence[str], None] = "60146069d841"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the existing primary key constraint
    op.drop_constraint("tts_pkey", "tts", type_="primary")

    # Create a new composite primary key
    op.create_primary_key("tts_pkey", "tts", ["text_hash", "engine"])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the composite primary key
    op.drop_constraint("tts_pkey", "tts", type_="primary")

    # Recreate the old single-column primary key
    op.create_primary_key("tts_pkey", "tts", ["text_hash"])
