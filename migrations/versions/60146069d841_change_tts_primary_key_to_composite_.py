"""Change TTS primary key to composite text_hash and engine

Revision ID: 60146069d841
Revises: b7395de96985
Create Date: 2025-07-01 12:28:01.354593

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "60146069d841"
down_revision: Union[str, Sequence[str], None] = "b7395de96985"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
