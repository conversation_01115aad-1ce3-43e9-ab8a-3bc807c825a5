"""Add YouTubeAudio table

Revision ID: 3be1321f525a
Revises: 01bfadd8b94f
Create Date: 2025-06-25 18:34:10.291940

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3be1321f525a"
down_revision: Union[str, Sequence[str], None] = "01bfadd8b94f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "youtubeaudio",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("video_id", sa.String(), nullable=False),
        sa.Column("file_path", sa.String(), nullable=False),
        sa.<PERSON>umn("file_name", sa.String(), nullable=False),
        sa.Column("file_size_bytes", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("video_id"),
    )
    op.create_index(
        op.f("ix_youtubeaudio_video_id"), "youtubeaudio", ["video_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_youtubeaudio_video_id"), table_name="youtubeaudio")
    op.drop_table("youtubeaudio")
    # ### end Alembic commands ###
