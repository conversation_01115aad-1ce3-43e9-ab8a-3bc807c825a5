"""Correct TaskType enum values and update existing lowercase entries

Revision ID: e2fb71b2359c
Revises: d63bbf6bc8d3
Create Date: 2025-07-01 10:05:57.070406

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e2fb71b2359c"
down_revision: Union[str, Sequence[str], None] = "d63bbf6bc8d3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 1. Temporarily change column type to text
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE TEXT USING task_type::text"
    )

    # 2. Update existing 'task_type' values from 'youtube_audio' to 'YOUTUBE_AUDIO'
    op.execute(
        "UPDATE task SET task_type = 'YOUTUBE_AUDIO' WHERE task_type = 'youtube_audio'"
    )

    # 3. Drop the old enum type if it exists
    op.execute("DROP TYPE IF EXISTS tasktype CASCADE")

    # 4. Create the new enum type with correct values
    sa.Enum(
        "SUBTITLES", "SUMMARIZATION", "YOUTUBE_AUDIO", "TTS", name="tasktype"
    ).create(op.get_bind())

    # 5. Change column type back to the new enum type
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE tasktype USING task_type::tasktype"
    )

    # 6. Re-add NOT NULL constraint
    op.execute("ALTER TABLE task ALTER COLUMN task_type SET NOT NULL")

    # Add back the auto-generated index changes
    op.drop_index(op.f("ix_tts_text_hash"), table_name="tts")
    op.create_index(op.f("ix_tts_text_hash"), "tts", ["text_hash"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 1. Temporarily change column type to text
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE TEXT USING task_type::text"
    )

    # 2. Drop the new enum type
    op.execute("DROP TYPE IF EXISTS tasktype CASCADE")

    # 3. Create the old enum type (including 'youtube_audio' for downgrade)
    sa.Enum(
        "SUBTITLES",
        "SUMMARIZATION",
        "youtube_audio",
        "YOUTUBE_AUDIO",
        "TTS",
        name="tasktype",
    ).create(op.get_bind())

    # 4. Change column type back to the old enum type
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE tasktype USING task_type::tasktype"
    )

    # 5. Re-add NOT NULL constraint
    op.execute("ALTER TABLE task ALTER COLUMN task_type SET NOT NULL")

    # Add back the auto-generated index changes for downgrade
    op.drop_index(op.f("ix_tts_text_hash"), table_name="tts")
    op.create_index(op.f("ix_tts_text_hash"), "tts", ["text_hash"], unique=True)
    # ### end Alembic commands ###
