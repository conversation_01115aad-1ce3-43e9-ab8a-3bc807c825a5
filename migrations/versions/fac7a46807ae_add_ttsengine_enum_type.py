"""Add ttsengine enum type

Revision ID: fac7a46807ae
Revises: bc43b8b85be0
Create Date: 2025-07-01 10:45:47.123922

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = "fac7a46807ae"
down_revision: Union[str, Sequence[str], None] = "bc43b8b85be0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    ttsengine_enum = postgresql.ENUM("EDGE", "GEMINI", name="ttsengine")
    ttsengine_enum.create(op.get_bind())

    op.alter_column(
        "tts",
        "engine",
        existing_type=sa.VARCHAR(),
        type_=ttsengine_enum,
        existing_nullable=False,
        postgresql_using="engine::ttsengine",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tts",
        "engine",
        existing_type=postgresql.ENUM("EDGE", "GEMINI", name="ttsengine"),
        type_=sa.VARCHAR(),
        existing_nullable=False,
    )

    ttsengine_enum = postgresql.ENUM("EDGE", "GEMINI", name="ttsengine")
    ttsengine_enum.drop(op.get_bind())
    # ### end Alembic commands ###
