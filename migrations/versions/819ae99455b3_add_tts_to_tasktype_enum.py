"""Add TTS to TaskType enum

Revision ID: 819ae99455b3
Revises: 49c037730b51
Create Date: 2025-07-01 09:58:37.020537

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "819ae99455b3"
down_revision: Union[str, Sequence[str], None] = "49c037730b51"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add 'TTS' to the TaskType enum
    op.execute("ALTER TYPE tasktype ADD VALUE 'TTS'")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the 'TTS' value from the TaskType enum
    # This involves recreating the enum and updating the table, as direct dropping is not supported
    op.execute("ALTER TYPE tasktype RENAME TO tasktype_old")
    op.execute(
        "CREATE TYPE tasktype AS ENUM ('SUBTITLES', 'SUMMARIZATION', 'YOUTUBE_AUDIO')"
    )
    op.execute(
        "ALTER TABLE task ALTER COLUMN task_type TYPE tasktype USING task_type::text::tasktype"
    )
    op.execute("DROP TYPE tasktype_old")
    # ### end Alembic commands ###
