"""Update created_at default in TTS model

Revision ID: 5cf462c4df6a
Revises: c6b4e9d5b36f
Create Date: 2025-07-01 14:24:32.971900

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "5cf462c4df6a"
down_revision: Union[str, Sequence[str], None] = "c6b4e9d5b36f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
