"""Add created_at to summaries table

Revision ID: e2a8891b1bb2
Revises: b9de824624af
Create Date: 2025-06-22 10:56:59.501232

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e2a8891b1bb2"
down_revision: Union[str, Sequence[str], None] = "b9de824624af"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("summaries", sa.Column("created_at", sa.DateTime(), nullable=True))
    op.execute("UPDATE summaries SET created_at = NOW() WHERE created_at IS NULL")
    op.alter_column(
        "summaries",
        "created_at",
        existing_type=sa.DateTime(),
        nullable=False,
        server_default=sa.func.now(),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("summaries", "created_at")
    # ### end Alembic commands ###
