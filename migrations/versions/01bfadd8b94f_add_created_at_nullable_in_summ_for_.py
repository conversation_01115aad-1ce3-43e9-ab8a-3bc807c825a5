"""Add created_at nullable in summ for unification

Revision ID: 01bfadd8b94f
Revises: e2a8891b1bb2
Create Date: 2025-06-22 11:08:54.452438

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "01bfadd8b94f"
down_revision: Union[str, Sequence[str], None] = "e2a8891b1bb2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
