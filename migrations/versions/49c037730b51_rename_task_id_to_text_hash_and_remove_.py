"""Rename task_id to text_hash and remove file_name in tts table

Revision ID: 49c037730b51
Revises: 0e6161d4f76c
Create Date: 2025-07-01 09:49:46.978021

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "49c037730b51"
down_revision: Union[str, Sequence[str], None] = "0e6161d4f76c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tts", sa.Column("text_hash", sa.String(), nullable=False))
    op.drop_index(op.f("ix_tts_task_id"), table_name="tts")
    op.create_index(op.f("ix_tts_text_hash"), "tts", ["text_hash"], unique=True)
    op.drop_column("tts", "file_name")
    op.drop_column("tts", "task_id")
    # Set new primary key on text_hash
    op.create_primary_key("tts_pkey", "tts", ["text_hash"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("tts_pkey", "tts", type_="primary")
    op.add_column("tts", sa.Column("task_id", sa.String(), nullable=False))
    op.add_column("tts", sa.Column("file_name", sa.String(), nullable=True))
    op.drop_index(op.f("ix_tts_text_hash"), table_name="tts")
    op.create_index(op.f("ix_tts_task_id"), "tts", ["task_id"], unique=True)
    op.drop_column("tts", "text_hash")
    # Re-add primary key on task_id for downgrade
    op.create_primary_key("tts_pkey", "tts", ["task_id"])
    # ### end Alembic commands ###
