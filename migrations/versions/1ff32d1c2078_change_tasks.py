"""change tasks

Revision ID: 1ff32d1c2078
Revises: 01e96a7412a3
Create Date: 2025-06-17 12:23:20.037085

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = "1ff32d1c2078"
down_revision: Union[str, Sequence[str], None] = "01e96a7412a3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "task",
        "id",
        existing_type=sa.UUID(),
        type_=sqlmodel.sql.sqltypes.AutoString(),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "task",
        "id",
        existing_type=sqlmodel.sql.sqltypes.AutoString(),
        type_=sa.UUID(),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
