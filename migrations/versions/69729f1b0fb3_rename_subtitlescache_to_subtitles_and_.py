"""Rename subtitlescache to subtitles and summarizationcache to summaries

Revision ID: 69729f1b0fb3
Revises: 1ff32d1c2078
Create Date: 2025-06-21 08:12:43.019527

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "69729f1b0fb3"
down_revision: Union[str, Sequence[str], None] = "1ff32d1c2078"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table("subtitlescache", "subtitles")
    op.rename_table("summarizationcache", "summaries")
    op.create_index(op.f("ix_summaries_mode"), "summaries", ["mode"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_summaries_mode"), table_name="summaries")
    op.rename_table("summaries", "summarizationcache")
    op.rename_table("subtitles", "subtitlescache")
    # ### end Alembic commands ###
