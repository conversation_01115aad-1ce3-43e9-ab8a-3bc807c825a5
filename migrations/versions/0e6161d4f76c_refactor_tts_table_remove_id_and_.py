"""Refactor tts table: remove id and reorder columns

Revision ID: 0e6161d4f76c
Revises: 6d830a3bf192
Create Date: 2025-07-01 09:41:36.636407

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "0e6161d4f76c"
down_revision: Union[str, Sequence[str], None] = "6d830a3bf192"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the index on task_id if it exists, to recreate it with unique=True
    op.drop_index(op.f("ix_tts_task_id"), table_name="tts")

    # Drop the 'id' column (this should implicitly drop its primary key constraint)
    op.drop_column("tts", "id")

    # Set 'task_id' as the new primary key
    op.create_primary_key("tts_pkey", "tts", ["task_id"])

    # Recreate the index on task_id, ensuring it's unique
    op.create_index(op.f("ix_tts_task_id"), "tts", ["task_id"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the new primary key constraint on 'task_id'
    op.drop_constraint("tts_pkey", "tts", type_="primary")

    # Add the 'id' column back
    op.add_column(
        "tts", sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False)
    )

    # Re-add the old primary key constraint on 'id'
    op.create_primary_key("tts_pkey", "tts", ["id"])

    # Re-create index on task_id with unique=True for downgrade
    op.drop_index(op.f("ix_tts_task_id"), table_name="tts")
    op.create_index(op.f("ix_tts_task_id"), "tts", ["task_id"], unique=True)
    # ### end Alembic commands ###
