"""Add tts table

Revision ID: 1cc88f62f47c
Revises: e73f0edd1a0d
Create Date: 2025-07-01 09:31:40.509616

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "1cc88f62f47c"
down_revision: Union[str, Sequence[str], None] = "e73f0edd1a0d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("youtubeaudio_video_id_key"), "youtubeaudio", type_="unique"
    )
    op.drop_index(op.f("ix_youtubeaudio_video_id"), table_name="youtubeaudio")
    op.create_index(
        op.f("ix_youtubeaudio_video_id"), "youtubeaudio", ["video_id"], unique=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_youtubeaudio_video_id"), table_name="youtubeaudio")
    op.create_index(
        op.f("ix_youtubeaudio_video_id"), "youtubeaudio", ["video_id"], unique=False
    )
    op.create_unique_constraint(
        op.f("youtubeaudio_video_id_key"),
        "youtubeaudio",
        ["video_id"],
        postgresql_nulls_not_distinct=False,
    )
    # ### end Alembic commands ###
