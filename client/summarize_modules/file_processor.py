import os
from pathlib import Path
from typing import Optional

import mdformat
from rich.console import <PERSON>sol<PERSON>

from .exceptions import FileProcessingError


class FileProcessor:
    def __init__(
        self, format_markdown: bool = False, console: Optional[Console] = None
    ):
        self.format_markdown = format_markdown
        self.console = console or Console()

    def get_summary_path(self, original_file_path: Path, mode: str) -> Path:
        """Constructs the expected path for the summary Markdown file."""
        base_name = original_file_path.stem
        md_path = (
            original_file_path.parent / f"{base_name}-{mode}.md"
            if mode != "default"
            else original_file_path.parent / f"{base_name}.md"
        )
        return md_path

    def does_summary_exist(self, original_file_path: Path, mode: str) -> bool:
        """Checks if the summary Markdown file already exists for the given original file and mode."""
        summary_path = self.get_summary_path(original_file_path, mode)
        return summary_path.exists()

    def read_file(self, file_path: Path) -> str:
        """Reads content from a text file, raising FileProcessingError on failure."""
        try:
            text = file_path.read_text(encoding="utf-8")
            if not text.strip():
                raise ValueError("File is empty or contains only whitespace.")
            return text
        except Exception as e:
            raise FileProcessingError(
                f"Error reading file {file_path.name}: {e}"
            ) from e

    def save_summary(self, summary: str, original_file_path: Path, mode: str):
        """Saves the summary to a Markdown file."""
        base_name = original_file_path.stem
        md_path = (
            original_file_path.parent / f"{base_name}-{mode}.md"
            if mode != "default"
            else original_file_path.parent / f"{base_name}.md"
        )

        if md_path.exists():
            self.console.print(
                f"[yellow]Предупреждение: Файл {md_path.name} уже существует. Пропускаем сохранение.[/yellow]"
            )
            # Optionally raise an error here if overwriting is not desired
            return

        try:
            if self.format_markdown:
                formatted_text = mdformat.text(
                    summary,
                    options={"wrap": "no", "number": True, "markdown_flavor": "gfm"},
                )
            else:
                formatted_text = summary
            md_path.write_text(formatted_text, encoding="utf-8")

            # Preserve original file's modification time
            source_stat = os.stat(original_file_path)
            os.utime(md_path, (source_stat.st_atime, source_stat.st_mtime))
        except Exception as e:
            raise FileProcessingError(
                f"Error saving summary to {md_path.name}: {e}"
            ) from e
