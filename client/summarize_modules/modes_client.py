from typing import Literal

# Доступные режимы суммаризации
AVAILABLE_MODES = [
    "default",
    "default#2",
    "default#3",
    "default#4",
    "default#5",
    "short#1",
    "short#2",
    "short#3",
    "short#4",
    "shortlite#1",
    "socratic#1",
    "socratic#2",
    "socratic#3",
    "socratic#4",
    "socratic#5",
    "diablo#1",
    "diablo#2",
    "diablo#3",
    "diablo#4",
    "diablo#5",
    "diablo#6",
    "diablo#7",
    "diablo#8",
    "detailed#1",
    "detailed#2",
    "detailed#3",
    "detailed#4",
    "detailed#5",
    "detailed#6",
    "detailed#7",
    "detailed#8",
    "detailed#9",
    "detailed#10",
    "detailed#11",
    "detailed#12",
    "detailed#13",
    "detailed#14",
    "detailed#15",
    "detailed#16",
    "detailed#17",
    "detailed#18",
    "detailed#19",
    "fireship#1",
    "fireship#2",
    "fireship#3",
    "fireship#4",
    "fireship#5",
    "fireship#6",
    "fireship#7",
    "fireship#8",
    "stevejobs#1",
]


def _get_modes_by_prefix(prefix: str) -> list[str]:
    """Получить все режимы с заданным префиксом из AVAILABLE_MODES"""
    return [mode for mode in AVAILABLE_MODES if mode.startswith(prefix + "#")]


# Мета-режимы (группы режимов)
META_MODES: dict[str, list[str]] = {
    "default": _get_modes_by_prefix("default"),
    "short": _get_modes_by_prefix("short"),
    "detailed": _get_modes_by_prefix("detailed"),
    "socratic": _get_modes_by_prefix("socratic"),
    "diablo": _get_modes_by_prefix("diablo"),
    "fireship": _get_modes_by_prefix("fireship"),
    "stevejobs": _get_modes_by_prefix("stevejobs"),
    # Специальные группы
    "education": ["detailed#15", "detailed#18", "detailed#19", "socratic#5"],
    "leader": ["detailed#16", "detailed#17"],
    "debate": ["diablo#1", "socratic#1", "socratic#5"],
    "fireship_basic": ["fireship#3", "fireship#4", "fireship#5"],
    "totaltest": AVAILABLE_MODES,
}

# Тип для режимов суммаризации
SummarizationMode = Literal[tuple(AVAILABLE_MODES)]


def get_meta_modes() -> dict[str, list[str]]:
    """Получить словарь мета-режимов"""
    return META_MODES


def is_meta_mode(mode: str) -> bool:
    """Проверить, является ли режим мета-режимом"""
    return mode in META_MODES


def get_modes_for_meta_mode(meta_mode: str) -> list[str]:
    """Получить список режимов для мета-режима"""
    return META_MODES.get(meta_mode, [])


def get_available_modes() -> list[str]:
    """Получить список доступных режимов суммаризации"""
    return AVAILABLE_MODES


def is_valid_mode(mode: str) -> bool:
    """Проверить, является ли режим допустимым"""
    return mode in AVAILABLE_MODES
