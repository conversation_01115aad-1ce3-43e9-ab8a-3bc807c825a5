class SummarizationError(Exception):
    """Base exception for summarization client errors."""

    pass


class AuthenticationError(SummarizationError):
    """Raised when API key is invalid or missing."""

    pass


class ServerUnavailableError(SummarizationError):
    """Raised when the summarization server is not reachable."""

    pass


class APITimeoutError(SummarizationError):
    """Raised when an API operation times out."""

    pass


class TaskFailedError(SummarizationError):
    """Raised when a summarization task fails on the server."""

    def __init__(self, message: str, error_details: str = None):
        super().__init__(message)
        self.error_details = error_details


class FileProcessingError(SummarizationError):
    """Raised for errors during file reading or writing."""

    pass


class SSEConnectionError(SummarizationError):
    """Raised when there's an issue with the SSE stream itself (e.g., unexpected disconnection)."""

    pass
