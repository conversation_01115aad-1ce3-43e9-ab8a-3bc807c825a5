# summarize_modules/console_manager.py

from pathlib import Path
from datetime import timedelta
from typing import List, Dict, Any
from collections import deque

from rich.panel import Panel
from rich.progress import (
    Progress,
    BarColumn,
    TextColumn,
    SpinnerColumn,
    TaskProgressColumn,
    TimeElapsedColumn,
    TaskID,
)
from rich.table import Table
from rich.console import Console, Group

from .config import LOG_FILE, BAR_WIDTH, MAX_VISIBLE_FILE_TASKS


def format_time(seconds: float) -> str:
    """Форматирует секунды в строку HH:MM:SS."""
    return str(timedelta(seconds=int(seconds)))


class ConsoleManager:
    """Управляет всем выводом в консоль с использованием Rich."""

    def __init__(self, console: Console):
        self.console = console
        self.progress: Progress | None = None
        # Двусторонняя очередь для эффективного добавления и удаления
        self._visible_file_tasks: deque[TaskID] = deque(maxlen=MAX_VISIBLE_FILE_TASKS)

    def display_plan(self, directory: Path, modes_to_run: List[str], num_files: int):
        """Отображает план выполнения."""
        self.console.print("\n[bold]План обработки:[/bold]")
        self.console.print(f" • [bold]Папка:[/bold] [cyan]{directory.resolve()}[/cyan]")
        self.console.print(
            f" • [bold]Режимы для запуска:[/bold] [cyan]{', '.join(modes_to_run)}[/cyan]"
        )
        self.console.print(
            f" • [bold]Обнаружено .txt файлов:[/bold] [cyan]{num_files}[/cyan]"
        )
        self.console.print(
            f" • [bold]Всего операций (файлы * режимы):[/bold] [cyan]{num_files * len(modes_to_run)}[/cyan]"
        )

    def create_progress_bar(self) -> Progress:
        """Создает и возвращает экземпляр Progress."""
        self.progress = Progress(
            SpinnerColumn(),
            "|",
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=BAR_WIDTH),
            TaskProgressColumn(),
            "|",
            TimeElapsedColumn(),
            "|",
            TextColumn("{task.fields[status]}"),
            console=self.console,
        )
        return self.progress

    def add_file_task(self, description: str) -> TaskID:
        """
        Добавляет новую задачу файла в прогресс-бар, скрывая самую старую,
        если превышен лимит. Создает эффект "прокрутки".
        """
        if not self.progress:
            raise RuntimeError("Progress bar has not been initialized.")

        # Если очередь уже заполнена, самая старая задача будет автоматически удалена
        # благодаря maxlen. Нам нужно только скрыть её в progress.
        if len(self._visible_file_tasks) == MAX_VISIBLE_FILE_TASKS:
            oldest_task_id = self._visible_file_tasks[
                0
            ]  # peek at the one that will be removed
            self.progress.update(oldest_task_id, visible=False)

        new_task_id = self.progress.add_task(description, total=None, status="⏳")
        self._visible_file_tasks.append(new_task_id)
        return new_task_id

    def hide_all_file_tasks(self):
        """Скрывает все оставшиеся видимые задачи файлов в конце."""
        if not self.progress:
            return
        for task_id in self._visible_file_tasks:
            self.progress.update(task_id, visible=False)
        self._visible_file_tasks.clear()

    def display_mode_summary(self, summary_data: Dict[str, Any]):
        """Выводит итоги по одному режиму."""
        self.console.print(
            f"[bold]Итоги по режиму '{summary_data['mode']}':[/bold] "
            f"[green]Успешно: {summary_data['success']}[/green], "
            f"[yellow]Пропущено: {summary_data['skipped']}[/yellow], "
            f"[red]С ошибками: {summary_data['errors']}[/red]"
        )
        self.console.print("-" * 30)

    def display_final_report(
        self, stats: Dict[str, Any], num_files: int, elapsed_time: float
    ):
        """Отображает итоговую панель с результатами."""
        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column()
        summary_table.add_column(style="bold", justify="right")
        summary_table.add_row(
            "Всего операций успешно:", f"[green]{stats['success']}[/green]"
        )
        summary_table.add_row(
            "Всего операций с ошибками:", f"[red]{stats['errors']}[/red]"
        )
        summary_table.add_row(
            "Всего операций пропущено:", f"[yellow]{stats['skipped']}[/yellow]"
        )
        summary_table.add_row("Всего .txt файлов в папке:", f"[cyan]{num_files}[/cyan]")
        summary_table.add_row(
            "Затрачено времени:", f"[yellow]{format_time(elapsed_time)}[/yellow]"
        )

        final_panel_content = [summary_table]
        if stats["details"]:
            errors_table = Table(
                title="[bold red]Детали ошибок[/bold red]", expand=True
            )
            errors_table.add_column("Файл", style="cyan", no_wrap=True)
            errors_table.add_column("Ошибка", style="red")
            for filename, error_msg in stats["details"]:
                errors_table.add_row(filename, error_msg)
            final_panel_content.append(errors_table)

        renderable_group = Group(*final_panel_content)
        self.console.print(
            Panel(
                renderable_group,
                title="[bold green]✅ Обработка завершена[/bold green]",
                border_style="blue",
                padding=(1, 2),
            )
        )
        if stats["errors"] > 0:
            self.console.print(
                f"[yellow]Для получения подробной информации об ошибках, проверьте лог-файл: {LOG_FILE}[/yellow]"
            )
