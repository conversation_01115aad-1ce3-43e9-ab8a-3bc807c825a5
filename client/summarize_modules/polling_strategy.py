import asyncio
import time
from pathlib import Path
from typing import Any, Optional

import httpx  # For specific httpx exceptions
from rich.console import Console
from rich.progress import Progress  # Type hint for progress object

from loguru import logger

from .api_client_http import APIClient
from .exceptions import AuthenticationError, APITimeoutError, TaskFailedError
from .interfaces import ISummarizationStrategy
from client.summarize_modules.file_utils import trim_filename


class PollingSummarizationStrategy(ISummarizationStrategy):
    def __init__(
        self,
        api_client: APIClient,
        console: Console,
        polling_interval: float = 2,
        operation_timeout: float = 300,
        debug: bool = False,
    ):
        self.api_client = api_client
        self.console = console
        self.polling_interval = polling_interval
        self.operation_timeout = operation_timeout
        self.debug = debug
        logger.debug(
            f"PollingSummarizationStrategy initialized with polling_interval={self.polling_interval}, operation_timeout={self.operation_timeout}."
        )

    async def process_file(
        self,
        file_path: Path,
        mode: str,
        text_content: str,
        progress: Progress = None,
        task_id: Any = None,
    ) -> Optional[str]:
        file_name = trim_filename(file_path.name)
        logger.debug(
            f"Starting process_file for {file_name} with mode '{mode}' using PollingStrategy."
        )
        if progress and task_id is not None:
            progress.update(
                task_id, description=f"[bold white]Отправка: {file_name}[/bold white]"
            )
            logger.debug(f"Progress task for {file_name} updated to 'Отправка'.")

        try:
            # 1. Initiate task
            logger.debug(f"Initiating summarization task for {file_name}...")
            start_response = await self.api_client.initiate_summarization_task(
                text=text_content, mode=mode
            )
            logger.debug(f"Task initiation response for {file_name}: {start_response}")

            # Handle instantly completed task
            if start_response.get("status") == "completed" and start_response.get(
                "result_url"
            ):
                logger.debug(
                    f"Task for {file_name} instantly completed (cached). Fetching result."
                )
                if progress and task_id is not None:
                    progress.update(
                        task_id, description=f"Получение результата (кеш): {file_name}"
                    )
                    logger.debug(
                        f"Progress task for {file_name} updated to 'Получение результата (кеш)'."
                    )
                final_result = await self.api_client.get_task_result(
                    start_response["result_url"]
                )
                summary = final_result.get("result", {}).get("summary")
                if not summary:
                    logger.error(
                        f"Instant result for {file_name} did not contain valid summary. Result: {final_result}"
                    )
                    raise ValueError(
                        f"Instant result for {file_name} did not contain valid summary."
                    )
                logger.debug(
                    f"Instant summary retrieved for {file_name}. Summary length: {len(summary)}."
                )
                return summary

            status_url = start_response.get("status_url")
            if not status_url:
                logger.error(
                    f"Server response for {file_name} did not include 'status_url'. Response: {start_response}"
                )
                raise ValueError(
                    f"Server response for {file_name} did not include 'status_url'."
                )
            logger.debug(f"Task for {file_name} created. Status URL: {status_url}")

            # 2. Poll for status
            start_time = time.time()
            logger.debug(
                f"Starting polling for {file_name}. Timeout: {self.operation_timeout}s."
            )
            while time.time() - start_time < self.operation_timeout:
                if progress and task_id is not None:
                    progress.update(
                        task_id, description=f"Проверка статуса: {file_name}"
                    )
                    logger.debug(
                        f"Progress task for {file_name} updated to 'Проверка статуса'."
                    )

                status_data = await self.api_client.get_task_status(status_url)
                current_status = status_data.get("status")
                logger.debug(
                    f"Current task status for {file_name}: {current_status}. Full status data: {status_data}"
                )

                if current_status == "completed":
                    result_url = status_data.get("result_url")
                    if not result_url:
                        logger.error(
                            f"Task for {file_name} completed but no 'result_url' provided. Status data: {status_data}"
                        )
                        raise ValueError(
                            f"Task for {file_name} completed but no 'result_url' provided."
                        )

                    if progress and task_id is not None:
                        progress.update(
                            task_id, description=f"Получение результата: {file_name}"
                        )
                        logger.debug(
                            f"Progress task for {file_name} updated to 'Получение результата'."
                        )

                    logger.debug(
                        f"Fetching final result for {file_name} from {result_url}"
                    )
                    final_result = await self.api_client.get_task_result(result_url)
                    summary = final_result.get("result", {}).get("summary")

                    if not summary:
                        logger.error(
                            f"Final result for {file_name} did not contain a valid summary. Result: {final_result}"
                        )
                        raise ValueError(
                            f"Final result for {file_name} did not contain a valid summary."
                        )
                    logger.debug(
                        f"Summary retrieved for {file_name}. Summary length: {len(summary)}."
                    )
                    return summary

                elif current_status == "failed":
                    error_details = status_data.get(
                        "error", "Неизвестная ошибка на сервере."
                    )
                    logger.error(
                        f"Summarization task failed for {file_name}: {error_details}. Status data: {status_data}"
                    )
                    raise TaskFailedError(
                        f"Summarization task failed for {file_name}", error_details
                    )

                elif current_status in ("pending", "processing"):
                    desc = "В очереди" if current_status == "pending" else "В обработке"
                    if progress and task_id is not None:
                        progress.update(task_id, description=f"{desc}: {file_name}")
                        logger.debug(
                            f"Progress task for {file_name} updated to '{desc}'."
                        )
                    logger.debug(
                        f"Waiting {self.polling_interval}s before next poll for {file_name}."
                    )
                    await asyncio.sleep(self.polling_interval)

                else:
                    logger.error(
                        f"Unknown task status received for {file_name}: '{current_status}'. Status data: {status_data}"
                    )
                    raise ValueError(
                        f"Unknown task status received for {file_name}: '{current_status}'"
                    )

            # If loop exits due to timeout
            logger.error(
                f"Operation timed out for {file_name} after {self.operation_timeout} seconds."
            )
            raise APITimeoutError(
                f"Operation timed out after {self.operation_timeout} seconds for {file_name}."
            )

        except (httpx.RequestError, httpx.TimeoutException) as e:
            logger.error(
                f"Network or timeout error during task processing for {file_name}: {e}"
            )
            # Catch network/connection errors specific to httpx and re-raise as custom timeout/general error
            raise APITimeoutError(
                f"Network error during task processing for {file_name}: {e}"
            ) from e
        except (ValueError, httpx.HTTPStatusError) as e:
            logger.error(f"API server error or parsing error for {file_name}: {e}")
            # Catch bad responses/parsing errors
            self.console.print(f"[red]Ошибка API сервера для {file_name}: {e}[/red]")
            raise
        except AuthenticationError:
            logger.error(f"AuthenticationError caught for {file_name}. Re-raising.")
            # Re-raise authentication error directly for main to handle sys.exit()
            raise
        except (APITimeoutError, TaskFailedError) as e:
            logger.error(
                f"Specific operational error caught for {file_name}: {e}. Re-raising."
            )
            # Re-raise specific operational errors for main to handle
            self.console.print(f"[red]Ошибка обработки {file_name}: {e.args[0]}[/red]")
            raise
        except Exception as e:
            # Catch any other unexpected errors during the polling process
            logger.exception(
                f"Unhandled exception during polling process for {file_name}."
            )
            self.console.print(
                f"[red]Неизвестная ошибка при обработке {file_name}: {e}[/red]"
            )
            raise
