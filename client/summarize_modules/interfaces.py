from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Optional


class ISummarizationStrategy(ABC):
    """Abstract base class for summarization strategies (e.g., Polling, SSE)."""

    @abstractmethod
    async def process_file(
        self,
        file_path: Path,
        mode: str,
        text_content: str,  # Pre-read content passed to strategy
        progress: Any = None,  # rich.progress.Progress instance (for UI updates)
        task_id: Any = None,  # rich.progress.TaskID (for UI updates)
    ) -> Optional[str]:
        """
        Processes a single file using the implemented strategy.
        Returns the summarized text on success, or None if the process failed
        but was handled internally (e.g., file skipped).
        Should raise specific exceptions for unrecoverable errors.
        """
        pass
