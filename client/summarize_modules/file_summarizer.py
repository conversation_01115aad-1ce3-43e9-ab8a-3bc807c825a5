# summarize_modules/file_summarizer.py

import asyncio
from pathlib import Path
from typing import List, Tuple, Any

import httpx
import typer
from loguru import logger
from rich.progress import Progress, TaskID

from .api_client_http import APIClient
from .file_processor import FileProcessor
from .console_manager import ConsoleManager
from .exceptions import (
    AuthenticationError,
    ServerUnavailableError,
    APITimeoutError,
    TaskFailedError,
    FileProcessingError,
)
from client.summarize_modules.file_utils import trim_filename


class FileSummarizer:
    """Отвечает за логику обработки набора файлов для одного режима."""

    def __init__(
        self,
        api_client: APIClient,
        summarization_strategy: Any,
        file_processor: FileProcessor,
        console_manager: ConsoleManager,
    ):
        self.api_client = api_client
        self.summarization_strategy = summarization_strategy
        self.file_processor = file_processor
        self.console_manager = console_manager
        logger.debug("FileSummarizer initialized.")

    async def connect_to_server(self):
        """Проверяет доступность сервера."""
        logger.info("Attempting to connect to the server...")
        if not await self.api_client.check_server_available():
            message = (
                f"Не удалось подключиться к серверу по адресу "
                f"{self.api_client.base_url}. Пожалуйста, запустите сервер."
            )
            logger.critical(message)
            raise ServerUnavailableError(message)
        logger.info("Server connection successful.")

    async def process_directory(
        self,
        directory: Path,
        mode: str,
        progress: Progress,
        overall_task_id: TaskID,
    ) -> Tuple[int, int, int, List[Tuple[str, str]], str]:
        """Обрабатывает все .txt файлы в директории для указанного режима."""
        logger.debug(f"Scanning directory: {directory} for mode '{mode}'.")
        txt_files = sorted(list(directory.glob("**/*.txt")))
        if not txt_files:
            logger.warning(f"No .txt files found in {directory}.")
            return 0, 0, 0, [], mode

        completed_count = 0
        error_details = []
        logger.info(
            f"Starting processing for mode '{mode}'. Found {len(txt_files)} files."
        )

        for txt_file in txt_files:
            short_name = trim_filename(txt_file.name)
            # Делегируем создание и управление видимостью задачи ConsoleManager
            file_task_id = self.console_manager.add_file_task(
                f"В очереди: {short_name}"
            )

            try:
                if self.file_processor.does_summary_exist(txt_file, mode):
                    logger.info(
                        f"Skipping {short_name}: Summary exists for mode '{mode}'."
                    )
                    progress.update(
                        file_task_id,
                        completed=1,
                        total=1,
                        description=f"[cyan]Пропущено ({mode}, есть): {short_name}[/cyan]",
                        status="⏩",
                    )
                else:
                    text_content = self.file_processor.read_file(txt_file)
                    result = await self.summarization_strategy.process_file(
                        file_path=txt_file,
                        mode=mode,
                        text_content=text_content,
                        progress=progress,
                        task_id=file_task_id,
                    )
                    if result["status"] == "completed" and result["summary"]:
                        self.file_processor.save_summary(
                            result["summary"], txt_file, mode
                        )
                        completed_count += 1
                        progress.update(
                            file_task_id,
                            description=f"[green]Готово ({mode}): {short_name}[/green]",
                            status="✅",
                        )
                    elif result["status"] == "failed":
                        error_msg = result.get("error", "Неизвестная ошибка")
                        error_details.append((short_name, error_msg))
                        progress.update(
                            file_task_id,
                            description=f"[red]Ошибка: {short_name}[/red]",
                            status="❌",
                        )
                    else:  # Skipped
                        logger.warning(
                            f"File {short_name} was skipped due to no valid response."
                        )
                        progress.update(
                            file_task_id,
                            description=f"[yellow]Пропущено ({mode}, нет ответа): {short_name}[/yellow]",
                            status="❔",
                        )

            except AuthenticationError as e:
                logger.critical(f"Authentication error, exiting: {e}")
                self.console_manager.console.print(
                    f"[bold red]❌ Критическая ошибка аутентификации: {e}. Проверьте API ключ.[/bold red]"
                )
                raise typer.Exit(code=1)
            except (
                APITimeoutError,
                TaskFailedError,
                FileProcessingError,
                httpx.RequestError,
                ValueError,
            ) as e:
                logger.error(f"Error processing {short_name}: {e}")
                progress.update(
                    file_task_id,
                    description=f"[red]Ошибка: {short_name}[/red]",
                    status="❌",
                )
                error_details.append((short_name, str(e)))
            except Exception:
                logger.exception(f"Unhandled exception while processing {short_name}")
                progress.update(
                    file_task_id,
                    description=f"[bold red]Критическая ошибка: {short_name}[/bold red]",
                    status="☠️",
                )
                error_details.append(
                    (short_name, "Неизвестная критическая ошибка. См. лог-файл.")
                )
            finally:
                progress.update(overall_task_id, advance=1)
                await asyncio.sleep(0.05)  # Небольшая задержка для плавности UI

        skipped_files_count = len(txt_files) - completed_count - len(error_details)
        logger.info(
            f"Finished for mode '{mode}'. Success: {completed_count}, Skipped: {skipped_files_count}, Errors: {len(error_details)}."
        )

        return (
            completed_count,
            len(error_details),
            skipped_files_count,
            error_details,
            mode,
        )
