# run_summary.py

import asyncio
from pathlib import Path

import typer
from loguru import logger
from rich.console import Console

# Используем относительные импорты из нашего пакета
from client.summarize_modules.config import (
    AppConfig,
    StrategyEnum,
    DEFAULT_HOST,
    DEFAULT_PORT,
    LOG_FILE,
)
from client.summarize_modules.app_runner import SummarizationApp
from client.summarize_modules.modes_client import get_modes_for_meta_mode

app = typer.Typer(
    help="Инструмент для асинхронной суммаризации текстовых файлов через API."
)


def configure_logging(debug: bool):
    """Настраивает логгер loguru для записи только в файл."""
    logger.remove()
    log_level = "DEBUG" if debug else "INFO"
    logger.add(
        LOG_FILE, level=log_level, rotation="10 MB", compression="zip", enqueue=True
    )
    logger.info("Logging configured. All logs will be written to file.")


@app.command()
def main(
    directory: Path = typer.Argument(
        ...,
        exists=True,
        file_okay=False,
        dir_okay=True,
        help="Директория с файлами для обработки.",
    ),
    mode: str = typer.Option(None, "--mode", "-m", help="Один режим суммаризации."),
    modes: str = typer.Option(None, "--modes", help="Несколько режимов через запятую."),
    meta_mode: str = typer.Option(None, "--meta-mode", help="Мета-режим."),
    host: str = typer.Option(
        DEFAULT_HOST, help=f"Хост сервера (умолч.: {DEFAULT_HOST})."
    ),
    port: int = typer.Option(
        DEFAULT_PORT, "-p", help=f"Порт сервера (умолч.: {DEFAULT_PORT})."
    ),
    format_md: bool = typer.Option(
        False, "--format", "-f", help="Форматировать Markdown."
    ),
    debug: bool = typer.Option(
        False, "--debug", "-d", help="Включить детальное логирование."
    ),
    api_key: str = typer.Option(
        None,
        "--api-key",
        envvar="SUMMARY_API_KEY",
        help="API ключ (или env SUMMARY_API_KEY).",
    ),
    strategy: StrategyEnum = typer.Option(
        StrategyEnum.sse.value,
        "--strategy",
        "-s",
        case_sensitive=False,
        help="Стратегия взаимодействия с API.",
    ),
):
    """Синхронная обёртка для асинхронной логики."""
    try:
        asyncio.run(
            main_async(
                directory,
                mode,
                modes,
                meta_mode,
                host,
                port,
                format_md,
                debug,
                api_key,
                strategy,
            )
        )
    except (typer.Abort, typer.Exit):
        pass  # Тихо выходим
    except Exception as e:
        Console().print(
            f"[bold red]Произошла непредвиденная критическая ошибка. Проверьте лог-файл: {LOG_FILE}[/bold red]"
        )
        logger.exception(f"A critical unhandled exception occurred: {e}")


async def main_async(
    directory: Path,
    mode: str,
    modes: str,
    meta_mode: str,
    host: str,
    port: int,
    format_md: bool,
    debug: bool,
    api_key: str,
    strategy: StrategyEnum,
):
    """Настраивает и запускает приложение."""
    console = Console()
    configure_logging(debug)

    mode_options = [mode, modes, meta_mode]
    if sum(1 for opt in mode_options if opt is not None) != 1:
        console.print(
            "[red]Ошибка: Укажите ровно один из параметров: --mode, --modes или --meta-mode[/red]"
        )
        raise typer.Exit(1)

    if mode:
        modes_to_run = [mode]
    elif modes:
        modes_to_run = [m.strip() for m in modes.split(",")]
    else:  # meta_mode
        modes_to_run = get_modes_for_meta_mode(meta_mode)
        if not modes_to_run:
            console.print(f"[red]Ошибка: Неизвестный мета-режим '{meta_mode}'[/red]")
            raise typer.Exit(1)

    config = AppConfig(host, port, api_key, strategy.value, format_md, debug)
    app_instance = SummarizationApp(config, directory, modes_to_run)
    await app_instance.run()


if __name__ == "__main__":
    app()
