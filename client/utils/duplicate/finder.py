import os
from pathlib import Path
from typing import Dict, List
from datetime import datetime

import xxhash
from loguru import logger
import questionary
from questionary import Style
from rich.console import Console
from send2trash import send2trash

# Define a custom style for blue questions
custom_style = Style(
    [
        ("qmark", "fg:#00BFFF bold"),  # token in front of the question
        ("question", "fg:#00BFFF bold"),  # question text
        ("answer", "fg:#FF9D00 bold"),  # submitted answer text behind the question
        ("pointer", "fg:#FF9D00 bold"),  # pointer used in select and checkbox prompts
        (
            "highlighted",
            "fg:#FF9D00 bold",
        ),  # pointed-at choice in select and checkbox prompts
        ("separator", "fg:#cc5454"),  # separator in lists
        ("instruction", ""),  # user instructions for select, rawselect, checkbox
        ("text", ""),  # plain text
        (
            "disabled",
            "fg:#858585 italic",
        ),  # disabled choices for select and checkbox prompts
    ]
)

console = Console()


class DuplicateFinder:
    def __init__(self):
        logger.info("DuplicateFinder initialized.")

    def _generate_xxh3_hash(self, filepath: Path) -> str:
        """Generates an XXH3 hash for a given file."""
        hasher = xxhash.xxh3_64()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(65536), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except IOError as e:
            logger.error(f"Error reading file {filepath}: {e}")
            return ""

    def find_duplicates(self, directory: str) -> Dict[str, List[Path]]:
        """Finds duplicate files in a given directory based on XXH3 hash."""
        logger.info(f"Starting duplicate search in directory: {directory}")
        hashes: Dict[str, List[Path]] = {}
        duplicates: Dict[str, List[Path]] = {}

        for filepath in Path(directory).rglob("*"):
            if filepath.is_file():
                file_hash = self._generate_xxh3_hash(filepath)
                if file_hash:
                    if file_hash in hashes:
                        if file_hash not in duplicates:
                            duplicates[file_hash] = [hashes[file_hash]]
                        duplicates[file_hash].append(filepath)
                    else:
                        hashes[file_hash] = filepath

        if not duplicates:
            console.print("[bold green]Дубликаты не найдены.[/bold green]")
        else:
            console.print(
                f"[bold yellow]Найдено {len(duplicates)} групп дубликатов.[/bold yellow]"
            )
            for file_hash, paths in duplicates.items():
                console.print(
                    f"  [cyan]Хэш:[/cyan] [bold magenta]{file_hash}[/bold magenta], [cyan]Файлы:[/cyan] {[f.name for f in paths]}"
                )
        return duplicates

    def _smart_select_file_to_keep(self, files: List[Path]) -> Path:
        """
        Selects the 'best' file to keep from a list of duplicates.
        Criteria:
        1. Oldest file (based on modification time).
        2. Shortest file name.
        3. File name does not contain 'Копия', 'Copy', or ends with a number in parentheses.
        """
        if not files:
            return None

        # Sort by modification time (oldest first)
        files.sort(key=lambda f: f.stat().st_mtime)

        # Filter out "copy" names and sort by length
        filtered_files = []
        for f in files:
            name_lower = f.name.lower()
            if (
                "копия" not in name_lower
                and "copy" not in name_lower
                and not f.stem.endswith(("copy", " копия"))
                and not f.stem.endswith(
                    ("(1)", "(2)", "(3)", "(4)", "(5)", "(6)", "(7)", "(8)", "(9)")
                )
            ):  # Simplified check for copy suffixes
                filtered_files.append(f)

        if filtered_files:
            # Sort by name length (shortest first)
            filtered_files.sort(key=lambda f: len(f.name))
            return filtered_files[0]
        else:
            # If all files have "copy" names, revert to oldest and then shortest name
            files.sort(key=lambda f: f.stat().st_mtime)
            files.sort(key=lambda f: len(f.name))
            return files[0]

    def process_duplicates(self, duplicates: Dict[str, List[Path]]):
        """Processes the found duplicate files (rename or delete)."""
        if not duplicates:
            logger.info("No duplicates to process.")
            return

        all_duplicates_to_process: List[Path] = []
        console.print("\n[bold blue]--- Найденные группы дубликатов ---[/bold blue]")
        for file_hash, paths in duplicates.items():
            console.print(
                f"  [bold underline]Группа (хэш: {file_hash}):[/bold underline]"
            )
            file_to_keep = self._smart_select_file_to_keep(paths)
            if not file_to_keep:
                console.print(
                    f"    [bold red]Не удалось определить файл для сохранения для хэша {file_hash}. Пропускаем группу.[/bold red]"
                )
                continue
            console.print(
                f"✅ [bold]{file_to_keep.name}[/bold] ([italic]{file_to_keep.parent}[/italic]) [dim](Изменен: {datetime.fromtimestamp(file_to_keep.stat().st_mtime).strftime('%d %b %Y в %H:%M:%S')})[/dim]"
            )

            current_group_duplicates = []
            for p in paths:
                if p != file_to_keep:
                    console.print(
                        f"❌ [bold]{p.name}[/bold] ([italic]{p.parent}[/italic]) [dim](Изменен: {datetime.fromtimestamp(p.stat().st_mtime).strftime('%d %b %Y в %H:%M:%S')})[/dim]"
                    )
                    current_group_duplicates.append(p)

            if current_group_duplicates:
                all_duplicates_to_process.extend(current_group_duplicates)
            else:
                console.print(
                    "    [dim]Нет дубликатов для обработки в этой группе.[/dim]"
                )
            # console.print("\n") # Add a newline for visual separation
            console.print("\n\n[dim]---[/dim]\n\n")  # Add a separator line

        console.print("[bold blue]------------------------------------[/bold blue]")

        if not all_duplicates_to_process:
            console.print(
                "[bold green]Всего дубликатов для обработки не найдено.[/bold green]"
            )
            return

        console.print(
            "\n[bold blue]### Действия с найденными дубликатами ###[/bold blue]"
        )
        console.print("[italic]Следующие файлы будут затронуты:[/italic]")
        for dup_file in all_duplicates_to_process:
            console.print(
                f"- [magenta]{dup_file.name}[/magenta] (в: [dim]{dup_file.parent}[/dim])"
            )

        action = questionary.select(
            "Что сделать со всеми найденными дубликатами?",
            choices=[
                "Переименовать все дубликаты",
                "Удалить все дубликаты (переместить в корзину)",
                "Составить отчет",
                "Ничего не делать (завершить)",
            ],
            default="Ничего не делать (завершить)",
            style=custom_style,
        ).ask()

        if action == "Переименовать все дубликаты":
            renamed_count = 0
            for dup_file in all_duplicates_to_process:
                new_name = dup_file.parent / f"duplicate_{dup_file.name}"
                try:
                    os.rename(dup_file, new_name)
                    logger.info(f"  Переименован '{dup_file.name}' в '{new_name.name}'")
                    renamed_count += 1
                except OSError as e:
                    logger.error(f"  Ошибка переименования файла {dup_file}: {e}")
            console.print(
                f"[bold green]Переименовано {renamed_count} файлов с добавлением префикса 'duplicate_'.[/bold green]"
            )
        elif action == "Удалить все дубликаты (переместить в корзину)":
            deleted_count = 0
            total_deleted_size = 0
            for dup_file in all_duplicates_to_process:
                try:
                    file_size = dup_file.stat().st_size
                    send2trash(dup_file)  # Use send2trash to move to trash
                    logger.info(f"  Удален '{dup_file.name}' (перемещен в корзину)")
                    deleted_count += 1
                    total_deleted_size += file_size
                except OSError as e:
                    logger.error(f"  Ошибка удаления файла {dup_file}: {e}")
            console.print(
                f"[bold green]Удалено {deleted_count} файлов, освобождено {total_deleted_size / 1024:.2f} КБ.[/bold green]"
            )
        elif action == "Составить отчет":
            self.generate_report(duplicates, all_duplicates_to_process)
        elif action == "Ничего не делать (завершить)":
            console.print("[bold green]Без изменений файлов.[/bold green]")

    def generate_report(
        self, duplicates: Dict[str, List[Path]], all_duplicates_to_process: List[Path]
    ):
        """Generates a formatted report of duplicate files and saves it to a file."""
        report_filename = datetime.now().strftime("duplicates_%Y%m%d-%H%M.log")

        report_console = Console(record=True)  # Create a console to record output

        report_console.print(
            "[bold blue]--- Отчет по дубликатам файлов ---[/bold blue]"
        )
        report_console.print(
            f"[cyan]Дата отчета:[/cyan] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        report_console.print(
            f"[cyan]Директория поиска:[/cyan] {Path.cwd()}"
        )  # Assuming current working directory for search
        report_console.print(
            "\n[bold underline]Найденные группы дубликатов:[/bold underline]"
        )

        if not duplicates:
            report_console.print("[bold green]Дубликаты не найдены.[/bold green]")
        else:
            for file_hash, paths in duplicates.items():
                report_console.print(
                    f"\n  [bold underline]Группа (хэш: {file_hash}):[/bold underline]"
                )
                file_to_keep = self._smart_select_file_to_keep(paths)
                if not file_to_keep:
                    report_console.print(
                        f"    [bold red]Не удалось определить файл для сохранения для хэша {file_hash}. Пропускаем группу.[/bold red]"
                    )
                    continue
                report_console.print(
                    f"✅ [bold]{file_to_keep.name}[/bold] ([italic]{file_to_keep.parent}[/italic]) [dim](Изменен: {datetime.fromtimestamp(file_to_keep.stat().st_mtime).strftime('%d %b %Y в %H:%M:%S')})[/dim]"
                )

                current_group_duplicates = []
                for p in paths:
                    if p != file_to_keep:
                        report_console.print(
                            f"❌ [bold]{p.name}[/bold] ([italic]{p.parent}[/italic]) [dim](Изменен: {datetime.fromtimestamp(p.stat().st_mtime).strftime('%d %b %Y в %H:%M:%S')})[/dim]"
                        )
                        current_group_duplicates.append(p)

        report_console.print(
            "\n[bold blue]------------------------------------[/bold blue]"
        )

        # Additional summary for the report
        if all_duplicates_to_process:
            report_console.print(
                f"\n[bold yellow]Всего дубликатов, подлежащих обработке:[/bold yellow] {len(all_duplicates_to_process)}"
            )
        else:
            report_console.print(
                "[bold green]Нет файлов, подлежащих обработке (дубликаты не найдены или уже обработаны).[/bold green]"
            )

        try:
            with open(report_filename, "w", encoding="utf-8") as f:
                f.write(report_console.export_text())
            console.print(
                f"[bold green]Отчет сохранен в файл: {report_filename}[/bold green]"
            )
        except IOError as e:
            console.print(f"[bold red]Ошибка сохранения отчета: {e}[/bold red]")
