"""
Base classes and common functionality for file splitting.
"""

import logging
from abc import ABC, abstractmethod
from pathlib import Path

logger = logging.getLogger(__name__)


class SplitConfig:
    """Configuration class for file splitting parameters."""

    def __init__(
        self,
        max_file_size: int = 250000,
        english_multiplier: float = 1.8,
        target_ratio: float = 0.75,
        min_ratio: float = 0.5,
    ):
        self.max_file_size = max_file_size
        self.english_multiplier = english_multiplier
        self.target_part_size = int(max_file_size * target_ratio)
        self.max_part_size = max_file_size
        self.min_part_size = int(max_file_size * min_ratio)

    def get_adjusted_sizes(self, language: str) -> tuple[int, int, int, int]:
        """Get size constants adjusted for the detected language."""
        if language == "english":
            return (
                int(self.max_file_size * self.english_multiplier),
                int(self.target_part_size * self.english_multiplier),
                int(self.max_part_size * self.english_multiplier),
                int(self.min_part_size * self.english_multiplier),
            )
        else:
            return (
                self.max_file_size,
                self.target_part_size,
                self.max_part_size,
                self.min_part_size,
            )


class BaseSplitter(ABC):
    """Abstract base class for file splitters."""

    def __init__(self, config: SplitConfig):
        self.config = config

    @abstractmethod
    def find_split_positions(self, content: str) -> list[int]:
        """Find positions where the content should be split."""

    def count_characters(self, file_path: str) -> int:
        """Count the number of characters in a file."""
        try:
            with open(file_path, encoding="utf-8") as file:
                content = file.read()
                return len(content)
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return 0

    def detect_language(self, text: str) -> str:
        """Heuristically detect if the text is primarily Russian or English."""
        # Count Cyrillic characters (Russian alphabet)
        cyrillic_count = sum(
            1 for char in text if "а" <= char.lower() <= "я" or char.lower() in "ёй"
        )

        # Count Latin characters (English alphabet)
        latin_count = sum(1 for char in text if "a" <= char.lower() <= "z")

        # If there are significantly more Cyrillic characters than Latin, consider it Russian
        if cyrillic_count > latin_count * 0.5:
            logger.debug(
                f"Detected Russian text (Cyrillic: {cyrillic_count}, Latin: {latin_count})"
            )
            return "russian"
        else:
            logger.debug(
                f"Detected English text (Cyrillic: {cyrillic_count}, Latin: {latin_count})"
            )
            return "english"

    def validate_split_points(
        self, split_points: list[int], content_length: int, max_part_size: int
    ) -> bool:
        """Validate that split points create acceptable parts."""
        if len(split_points) <= 2:
            return False

        for i in range(len(split_points) - 1):
            part_size = split_points[i + 1] - split_points[i]
            if part_size > max_part_size:
                logger.warning(
                    f"Part {i + 1} size ({part_size}) exceeds maximum allowed size ({max_part_size})"
                )
                return False
        return True

    def split_file(self, file_path: str) -> bool:
        """Split a file into parts based on the splitter's strategy."""
        try:
            # Read file content
            with open(file_path, encoding="utf-8") as file:
                content = file.read()

            file_size = len(content)

            # Detect language and get appropriate size constants
            language = self.detect_language(content)
            max_file_size, target_part_size, max_part_size, min_part_size = (
                self.config.get_adjusted_sizes(language)
            )

            # Check if file needs splitting
            if file_size <= max_file_size:
                logger.info(
                    f"File {file_path} is smaller than {max_file_size} characters ({language} text), skipping."
                )
                return False

            logger.info(
                f"Processing file: {file_path} ({file_size} characters, {language} text)"
            )

            # Find split points using the specific splitter strategy
            split_points = self.find_split_positions(content)

            # Validate split points
            if not self.validate_split_points(split_points, file_size, max_part_size):
                logger.warning(f"Could not find suitable split points for {file_path}")
                return False

            # Split the file and save parts
            return self._save_parts(file_path, content, split_points)

        except Exception as e:
            logger.error(f"Error splitting file {file_path}: {e}")
            return False

    def _save_parts(
        self, file_path: str, content: str, split_points: list[int]
    ) -> bool:
        """Save the split parts to separate files."""
        file_path_obj = Path(file_path)
        base_name = file_path_obj.stem
        extension = file_path_obj.suffix
        directory = file_path_obj.parent

        for i in range(len(split_points) - 1):
            start = split_points[i]
            end = split_points[i + 1]

            # Extract content for this part
            part_content = content[start:end]

            # Apply any post-processing (can be overridden by subclasses)
            part_content = self._post_process_part(
                part_content, i, len(split_points) - 1
            )

            # Create part filename
            part_filename = f"{base_name}_part{i + 1}{extension}"
            part_path = directory / part_filename

            # Save part
            with open(part_path, "w", encoding="utf-8") as part_file:
                part_file.write(part_content)

            logger.info(
                f"Created part {i + 1}: {part_path} ({len(part_content)} characters)"
            )

        return True

    def _post_process_part(
        self, content: str, part_index: int, total_parts: int
    ) -> str:
        """Post-process part content. Can be overridden by subclasses."""
        return content


def process_directory(
    splitter: BaseSplitter, directory: str, pattern: str
) -> tuple[int, int]:
    """Process all files in a directory using the given splitter."""
    directory_path = Path(directory)
    if not directory_path.exists() or not directory_path.is_dir():
        logger.error(f"Directory {directory} does not exist or is not a directory")
        return 0, 0

    # Find all files matching the pattern
    file_paths = list(directory_path.glob(pattern))

    if not file_paths:
        logger.warning(f"No files matching pattern '{pattern}' found in {directory}")
        return 0, 0

    logger.info(
        f"Found {len(file_paths)} files matching pattern '{pattern}' in {directory}"
    )

    # Process each file
    files_processed = 0
    files_split = 0

    for file_path in file_paths:
        # Skip files that are already parts
        if "_part" in file_path.stem and file_path.stem.split("_part")[-1].isdigit():
            logger.info(f"Skipping part file: {file_path}")
            continue

        files_processed += 1
        if splitter.split_file(str(file_path)):
            files_split += 1

    return files_processed, files_split
