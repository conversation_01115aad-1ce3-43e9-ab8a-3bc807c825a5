import logging
from pathlib import Path
from xml.etree import ElementTree as ET
import re
import html

from client.utils.splitters.base import BaseSplitter, SplitConfig

logger = logging.getLogger(__name__)


def _serialize_plain_text_fb2(write, elem):
    """Рекурсивно извлекает текст из XML-элемента FB2 файла."""
    tag = elem.tag
    text = elem.text

    # Игнорируем комментарии и инструкции обработки
    if tag is ET.Comment or tag is ET.ProcessingInstruction:
        pass
    else:
        # Обрабатываем текст элемента
        if text:
            # Декодируем HTML-сущности
            decoded_text = html.unescape(text)
            # Добавляем перенос строки после заголовков, абзацев и других элементов
            if tag and (
                tag.endswith("}title")
                or tag.endswith("}p")
                or tag.endswith("}subtitle")
                or tag.endswith("}epigraph")
                or tag.endswith("}annotation")
                or tag.endswith("}cite")
            ):
                decoded_text = decoded_text.strip()
                if decoded_text:
                    # Проверяем, заканчивается ли текст знаком препинания
                    if decoded_text[-1] not in ".!?:;…":
                        decoded_text += "."
                    write(decoded_text)
                    write("\n")
            else:
                write(decoded_text)

        # Рекурсивно обрабатываем дочерние элементы
        for e in elem:
            _serialize_plain_text_fb2(write, e)

    # Обрабатываем хвостовой текст элемента
    if elem.tail:
        tail_text = html.unescape(elem.tail)
        # Если хвостовой текст содержит значимый контент и не заканчивается знаком препинания,
        # и предыдущий элемент был абзацем или заголовком, добавляем точку
        stripped_tail = tail_text.strip()
        if (
            stripped_tail
            and tag
            and (
                tag.endswith("}title")
                or tag.endswith("}p")
                or tag.endswith("}subtitle")
                or tag.endswith("}epigraph")
                or tag.endswith("}annotation")
                or tag.endswith("}cite")
            )
        ):
            if stripped_tail[-1] not in ".!?:;…":
                tail_text = (
                    tail_text.rstrip() + "." + tail_text[len(tail_text.rstrip()) :]
                )
        write(tail_text)


def to_plain_text_fb2(element):
    """Преобразует XML-элемент FB2 в чистый текст."""
    data = []
    write = data.append
    _serialize_plain_text_fb2(write, element)
    text = "".join(data)

    # Пост-обработка текста
    # Удаляем лишние пробелы, но сохраняем переносы строк
    text = re.sub(r" +", " ", text)  # Заменяем множественные пробелы одним
    text = re.sub(
        r"\n\s*\n\s*\n+", "\n\n", text
    )  # Ограничиваем максимум двумя переносами строк подряд
    text = text.strip()

    return text


class Fb2ChapterSplitter(BaseSplitter):
    """
    Splitter for FB2 files, extracting chapters and saving them as plain text.
    """

    def __init__(self, config: SplitConfig):
        super().__init__(config)
        # FB2 files are XML, so we don't need character-based max_file_size for chapter splitting,
        # but the config might still be used for other base splitter functionalities.
        # We will mainly focus on structural splitting here.

    def find_split_positions(self, content: str) -> list[int]:
        # This method is primarily designed for character-based splitting,
        # but for FB2, we will use a different approach for chapter extraction
        # that doesn't rely on character positions for "split points".
        # We will instead return a dummy list to satisfy the abstract method,
        # and handle the actual chapter extraction in a dedicated method or during _save_parts.
        # Returning [0, len(content)] will make BaseSplitter pass the whole content
        # to _save_parts as a single "part" to be further processed by us.
        return [0, len(content)]

    def validate_split_points(
        self, split_points: list[int], content_length: int, max_part_size: int
    ) -> bool:
        """
        For FB2 splitting, we don't rely on character-based split points validation.
        The actual splitting success is determined by the chapter extraction in _save_parts.
        """
        return True

    # Override the base split_file to handle output_dir
    def split_file(self, file_path: Path, output_base_dir: Path) -> bool:
        """Split a FB2 file into parts (chapters) and save them to a specific output directory."""
        try:
            # Read file content
            with open(file_path, encoding="utf-8") as file:
                content = file.read()
            return self._save_parts(
                file_path, content, [0, len(content)], output_base_dir
            )

        except Exception as e:
            logger.error(f"Error splitting FB2 file {file_path}: {e}")
            return False

    def _save_parts(
        self,
        file_path: Path,
        content: str,
        split_points: list[int],
        output_base_dir: Path,
    ) -> bool:
        """
        Overrides the base method to save FB2 chapters as separate text files
        within a subdirectory of the provided output_base_dir.
        """
        original_file_name = file_path.stem
        output_directory = output_base_dir / original_file_name
        output_directory.mkdir(parents=True, exist_ok=True)

        logger.info(f"Processing FB2 file: {file_path}")

        try:
            root = ET.fromstring(content)
        except ET.ParseError as e:
            logger.error(f"Error parsing FB2 file {file_path}: {e}")
            return False

        # Find all body elements, typically there's one main body
        bodies = root.findall(".//{http://www.gribuser.ru/xml/fictionbook/2.0}body")
        if not bodies:
            logger.warning(f"No <body> found in {file_path}. Cannot split by chapters.")
            return False

        chapter_count = 0
        for body in bodies:
            # Chapters are often <section> elements within <body>
            # Or sometimes directly within <body:section>
            for section_index, section in enumerate(
                body.findall(".//{http://www.gribuser.ru/xml/fictionbook/2.0}section")
            ):
                chapter_count += 1
                current_chapter_title = ""  # Initialize with empty string
                title_elem = section.find(
                    ".//{http://www.gribuser.ru/xml/fictionbook/2.0}title"
                )
                if title_elem is not None:
                    # Extract text from all p tags within the title
                    title_parts = [
                        p.text
                        for p in title_elem.findall(
                            ".//{http://www.gribuser.ru/xml/fictionbook/2.0}p"
                        )
                        if p.text
                    ]
                    if title_parts:
                        # Join title parts and clean up extra whitespace
                        current_chapter_title = " ".join(title_parts).strip()
                        # Sanitize filename for chapter title, remove invalid characters
                        current_chapter_title = re.sub(
                            r'[\\/:*?"<>|]', "", current_chapter_title
                        )
                        # Truncate title if too long for filename
                        if len(current_chapter_title) > 100:
                            current_chapter_title = (
                                current_chapter_title[:100].strip() + "..."
                            )

                # Extract plain text content of the section
                # Create a new root for the section to clean it independently
                plain_text_chapter = to_plain_text_fb2(section).strip()

                # If no meaningful title was extracted, use the first non-empty line of the content
                if not current_chapter_title:
                    first_line = next(
                        (
                            line
                            for line in plain_text_chapter.split("\n")
                            if line.strip()
                        ),
                        None,
                    )
                    if first_line:
                        current_chapter_title = re.sub(
                            r'[\\/:*?"<>|]', "", first_line
                        ).strip()
                        if len(current_chapter_title) > 100:
                            current_chapter_title = (
                                current_chapter_title[:100].strip() + "..."
                            )
                    else:
                        current_chapter_title = (
                            "Глава"  # Fallback if even content is empty
                        )

                # Ensure filename is not too long
                if (
                    len(current_chapter_title) > 150
                ):  # Slightly higher limit for potential prefix
                    current_chapter_title = current_chapter_title[:150].strip() + "..."

                # Сохраняем двойные переносы строк между абзацами, но удаляем тройные и более
                plain_text_chapter = re.sub(
                    r"\\n\\s*\\n\\s*\\n+", "\\n\\n", plain_text_chapter
                )
                plain_text_chapter = re.sub(r" +", " ", plain_text_chapter)

                # Проверяем, содержит ли глава текст (не только пробелы и переносы строк)
                if not re.sub(r"[\\s\\n]+", "", plain_text_chapter):
                    logger.warning(
                        f"Skipping empty chapter {current_chapter_title} in {file_path}"
                    )
                    continue

                output_filename = f"{chapter_count:02d} - {current_chapter_title}.txt"
                output_filepath = output_directory / output_filename

                with open(output_filepath, "w", encoding="utf-8") as f:
                    f.write(plain_text_chapter)
                logger.info(f"Saved chapter: {output_filepath}")

        if chapter_count == 0:
            logger.warning(f"No chapters (sections) found in FB2 file: {file_path}")
            return False

        return True
