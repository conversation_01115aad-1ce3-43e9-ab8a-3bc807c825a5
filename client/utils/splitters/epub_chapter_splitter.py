from pathlib import Path
from typing import List
import re

import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
from loguru import logger

from client.utils.splitters.base import BaseSplitter, SplitConfig


def clean_html(html_content: str) -> str:
    """Remove HTML tags from the content and return plain text."""
    soup = BeautifulSoup(html_content, "html.parser")
    return soup.get_text(separator=" ", strip=True)


def get_chapter_title_from_item(item: epub.EpubItem) -> str:
    """
    Extracts a cleaner title from an EPUB item's content.
    Looks for h1-h6 tags or the first significant line of text.
    """
    if not item.get_type() == ebooklib.ITEM_DOCUMENT:
        return ""

    soup = BeautifulSoup(item.content.decode("utf-8"), "html.parser")

    # Try to find a prominent heading (h1-h6)
    for i in range(1, 7):
        heading = soup.find(f"h{i}")
        if heading and heading.get_text(strip=True):
            return heading.get_text(strip=True)

    # If no heading, take the first significant line of text
    first_paragraph = soup.find("p")
    if first_paragraph and first_paragraph.get_text(strip=True):
        return first_paragraph.get_text(strip=True)

    # Fallback to a generic title if nothing suitable is found
    return ""


class EpubChapterSplitter(BaseSplitter):
    """Splits an EPUB file into parts based on its chapters."""

    def __init__(self, config: SplitConfig):
        super().__init__(config)

    def find_split_positions(self, content: str) -> List[int]:
        """This method is not used directly for EPUB as splitting is chapter-based.
        It's here to satisfy the BaseSplitter abstract method requirement.
        The actual splitting logic is in split_file.
        """
        return [0, len(content)]  # Placeholder

    def split_file(self, file_path: Path, output_base_dir: Path) -> bool:
        """Splits an EPUB file by its chapters and saves them as text files."""
        try:
            book = epub.read_epub(file_path)
        except Exception as e:
            logger.error(f"Ошибка при чтении EPUB файла {file_path}: {e}")
            return False

        # Create the subdirectory for this specific EPUB file
        file_output_dir = output_base_dir / file_path.stem
        file_output_dir.mkdir(parents=True, exist_ok=True)

        chapter_data = []  # Store tuples of (title, cleaned_text)
        for item in book.get_items():
            if item.get_type() == ebooklib.ITEM_DOCUMENT:
                chapter_title = get_chapter_title_from_item(item)
                cleaned_text = clean_html(item.content.decode("utf-8"))
                chapter_data.append((chapter_title, cleaned_text))

        if not chapter_data:
            logger.warning(f"Не найдено содержимого глав в файле {file_path.name}")
            return False

        full_content = "\n\n".join([text for _, text in chapter_data])

        # Now, split the full content into parts based on max_file_size, just like other splitters
        # The 'chapters' here are more like logical sections for EPUB, not strict splits
        # The actual splitting will be done by character count as per BaseSplitter logic

        language = self.detect_language(full_content)
        max_file_size, target_part_size, max_part_size, min_part_size = (
            self.config.get_adjusted_sizes(language)
        )

        # If the whole content is small enough, save as one file
        if len(full_content) <= max_file_size:
            output_file_path = file_output_dir / f"{file_path.stem}.txt"
            with open(output_file_path, "w", encoding="utf-8") as f:
                f.write(full_content)
            logger.info(
                f"Создан один файл: {output_file_path} ({len(full_content)} символов)"
            )
            return True

        # Proceed with splitting based on character limits
        # This part of logic needs to be revisited to align with chapter-based saving
        # For now, we will create dummy split points to pass the full content to _save_parts
        # and handle actual chapter splitting there.
        # However, the current _save_parts iterates based on character split points.
        # This means we need to adjust _save_parts to handle chapter_data and then
        # potentially split chapters if they exceed max_part_size.

        # New approach: collect actual chapters and their content, then process them
        # to ensure they meet size constraints.
        processed_chapters_for_saving = []
        for chapter_title, chapter_text in chapter_data:
            if len(chapter_text) <= max_part_size:
                processed_chapters_for_saving.append((chapter_title, chapter_text))
            else:
                # If a single chapter is too large, split it into sub-parts
                # using the base splitter's text splitting logic
                sub_split_points = self.find_split_positions_text(chapter_text)
                sub_parts = [
                    chapter_text[sub_split_points[j] : sub_split_points[j + 1]]
                    for j in range(len(sub_split_points) - 1)
                ]
                for idx, sub_part in enumerate(sub_parts):
                    # For sub-parts, append a numerical suffix to the chapter title
                    sub_part_title = (
                        f"{chapter_title}_part{idx + 1}"
                        if chapter_title
                        else f"Часть_{idx + 1}"
                    )
                    processed_chapters_for_saving.append((sub_part_title, sub_part))

        return self._save_parts(
            file_path, processed_chapters_for_saving, file_output_dir
        )

    def _save_parts(
        self,
        original_file_path: Path,
        chapters_data: List[tuple[str, str]],  # List of (title, content)
        output_dir: Path,
    ) -> bool:
        """Save the split parts (chapters) to separate files with chapter-based naming."""
        # base_name = original_file_path.stem

        for i, (chapter_title_raw, part_content) in enumerate(chapters_data):
            # Sanitize filename for chapter title, remove invalid characters
            chapter_title = re.sub(r'[\/:*?"<>|]', "", chapter_title_raw).strip()

            if not chapter_title:
                # If no meaningful title, use the first non-empty line of the content
                first_line = next(
                    (line for line in part_content.split("\n") if line.strip()), None
                )
                if first_line:
                    chapter_title = re.sub(r'[\/:*?"<>|]', "", first_line).strip()
                    # Truncate if too long
                    if len(chapter_title) > 100:
                        chapter_title = chapter_title[:100].strip() + "..."
                else:
                    chapter_title = "Глава"  # Fallback if even content is empty

            # Ensure a unique filename, especially if titles are repeated or generated generically
            # Add a numerical suffix to ensure uniqueness and order
            output_filename = f"{i + 1:02d} - {chapter_title}.txt"  # Changed to 0-padded index + chapter title

            # Truncate the filename if it's too long for the OS
            if (
                len(output_filename) > 200
            ):  # Arbitrary but reasonable limit for filenames
                output_filename = output_filename[:200] + ".txt"

            part_path = output_dir / output_filename

            with open(part_path, "w", encoding="utf-8") as part_file:
                part_file.write(part_content)

            logger.info(
                f"Создан файл главы {i + 1}: {part_path} ({len(part_content)} символов)"
            )
        return True
