"""
Heuristic-based file splitter that uses transition words and natural language patterns.
"""

import logging
import re

from .base import BaseSplitter, SplitConfig

logger = logging.getLogger(__name__)


class HeuristicSplitter(BaseSplitter):
    """Splitter that uses heuristic methods to find natural breaking points."""

    # Common English transition words that can serve as natural breaking points
    # Level 1: High priority words that are most likely to indicate the start of a new thought or section
    ENGLISH_TRANSITION_WORDS_LEVEL1 = {
        "so",
        "okay",
        "well",
        "now",
        "first",
        "second",
        "third",
        "finally",
        "lastly",
        "actually",
        "basically",
        "essentially",
        "honestly",
        "frankly",
        "anyway",
        "anyhow",
        "alright",
        "ok",
        "let's",
        "let us",
        "let me",
    }

    # Level 2: Lower priority words that may indicate transitions but are more common
    ENGLISH_TRANSITION_WORDS_LEVEL2 = {
        "and",
        "but",
        "or",
        "because",
        "therefore",
        "thus",
        "hence",
        "consequently",
        "however",
        "nevertheless",
        "nonetheless",
        "although",
        "though",
        "besides",
        "furthermore",
        "moreover",
        "meanwhile",
        "subsequently",
        "eventually",
        "ultimately",
        "then",
    }

    # Common Russian transition words that can serve as natural breaking points
    # Level 1: High priority words that are most likely to indicate the start of a new thought or section
    RUSSIAN_TRANSITION_WORDS_LEVEL1 = {
        "итак",
        "так",
        "ладно",
        "окей",
        "теперь",
        "затем",
        "во-первых",
        "во-вторых",
        "в-третьих",
        "наконец",
        "в заключение",
        "фактически",
        "в основном",
        "по сути",
        "честно говоря",
        "откровенно",
        "давайте",
        "позвольте",
        "дальше",
        "далее",
        "начнем",
        "продолжим",
        "вернемся",
        "посмотрим",
        "рассмотрим",
        "перейдем",
    }

    # Level 2: Lower priority words that may indicate transitions but are more common
    RUSSIAN_TRANSITION_WORDS_LEVEL2 = {
        "и",
        "но",
        "или",
        "потому",
        "поэтому",
        "следовательно",
        "таким образом",
        "в результате",
        "однако",
        "тем не менее",
        "несмотря на",
        "хотя",
        "впрочем",
        "в любом случае",
        "кроме того",
        "более того",
        "кстати",
        "между тем",
        "впоследствии",
        "в конечном счете",
        "в конце концов",
        "в итоге",
    }

    def __init__(self, config: SplitConfig):
        super().__init__(config)

        # Combine all transition words by level
        self.transition_words_level1 = self.ENGLISH_TRANSITION_WORDS_LEVEL1.union(
            self.RUSSIAN_TRANSITION_WORDS_LEVEL1
        )
        self.transition_words_level2 = self.ENGLISH_TRANSITION_WORDS_LEVEL2.union(
            self.RUSSIAN_TRANSITION_WORDS_LEVEL2
        )
        self.transition_words = self.transition_words_level1.union(
            self.transition_words_level2
        )

        # Create regex patterns
        self.transition_pattern_level1 = r"(?:^|\s+)({})[\s,.:]".format(
            "|".join(self.transition_words_level1)
        )
        self.transition_pattern_level2 = r"(?:^|\s+)({})[\s,.:]".format(
            "|".join(self.transition_words_level2)
        )
        self.transition_pattern = r"(?:^|\s+)({})[\s,.:]".format(
            "|".join(self.transition_words)
        )

    def find_transition_positions(self, content: str, level: int = 0) -> list[int]:
        """Find all positions of transition words in the content based on priority level."""
        transition_positions = [0]  # Start position

        # Select the appropriate pattern based on the level
        if level == 1:
            pattern = self.transition_pattern_level1
        elif level == 2:
            pattern = self.transition_pattern_level2
        else:  # level == 0 or any other value
            pattern = self.transition_pattern

        # Find all occurrences of transition words (case insensitive)
        for match in re.finditer(pattern, content, re.IGNORECASE):
            # Add the position where the actual transition word starts (not the whitespace before it)
            word_start = match.start() + match.group(0).index(match.group(1))
            transition_positions.append(word_start)

        # Add the end position
        transition_positions.append(len(content))

        return sorted(set(transition_positions))  # Remove duplicates and ensure order

    def find_sentence_boundaries(self, content: str) -> list[int]:
        """Find positions that might be sentence boundaries."""
        boundary_positions = [0]  # Start position

        # Find all occurrences of potential sentence endings followed by spaces
        # This pattern includes standard punctuation and Russian-specific punctuation
        for match in re.finditer(r"[.!?…;:][\s]+", content):
            boundary_positions.append(match.end())

        # Add the end position
        boundary_positions.append(len(content))

        return sorted(set(boundary_positions))  # Remove duplicates and ensure order

    def find_word_boundaries(self, content: str) -> list[int]:
        """Find positions between words (spaces) as a last resort method."""
        boundary_positions = [0]  # Start position

        # Find all occurrences of spaces
        for match in re.finditer(r"\s+", content):
            boundary_positions.append(match.end())

        # Add the end position
        boundary_positions.append(len(content))

        return sorted(set(boundary_positions))  # Remove duplicates and ensure order

    def find_split_positions(self, content: str) -> list[int]:
        """Determine optimal split points based on transition words and target size."""
        total_length = len(content)

        # Detect language and get appropriate size constants
        language = self.detect_language(content)
        max_file_size, target_part_size, max_part_size, min_part_size = (
            self.config.get_adjusted_sizes(language)
        )

        logger.info(
            f"Detected language: {language}, applying size multiplier: "
            f"{self.config.english_multiplier if language == 'english' else 1.0}"
        )
        logger.debug(
            f"Adjusted constants - MAX_FILE_SIZE: {max_file_size}, "
            f"TARGET_PART_SIZE: {target_part_size}, MAX_PART_SIZE: {max_part_size}, "
            f"MIN_PART_SIZE: {min_part_size}"
        )

        # If the file is small enough, return it as is
        if total_length <= max_file_size:
            return [0, total_length]

        # Try to find high-priority transition words first (level 1)
        level1_positions = self.find_transition_positions(content, level=1)
        logger.debug(f"Found {len(level1_positions) - 2} level 1 transition words")

        # If we don't have enough high-priority transition words, add low-priority ones (level 2)
        all_positions = level1_positions
        if len(level1_positions) < 5:  # Arbitrary threshold
            level2_positions = self.find_transition_positions(content, level=2)
            logger.debug(f"Found {len(level2_positions) - 2} level 2 transition words")
            all_positions = sorted(set(level1_positions + level2_positions))

        # If we still don't have enough transition words, also look for sentence boundaries
        if len(all_positions) < 5:  # Arbitrary threshold
            sentence_positions = self.find_sentence_boundaries(content)
            logger.debug(f"Found {len(sentence_positions) - 2} sentence boundaries")
            all_positions = sorted(set(all_positions + sentence_positions))

        # If we still don't have enough potential split points, use word boundaries
        if len(all_positions) < 10:  # Another arbitrary threshold
            word_positions = self.find_word_boundaries(content)
            logger.debug(f"Found {len(word_positions) - 2} word boundaries")
            all_positions = sorted(set(all_positions + word_positions))

        # Skip the first and last positions (they're just the start and end)
        usable_positions = all_positions[1:-1]

        if not usable_positions:
            logger.warning("No usable split points found, cannot split the file")
            return [0, total_length]

        return self._determine_optimal_splits(
            total_length,
            usable_positions,
            target_part_size,
            max_part_size,
            min_part_size,
        )

    def _determine_optimal_splits(
        self,
        total_length: int,
        usable_positions: list[int],
        target_part_size: int,
        max_part_size: int,
        min_part_size: int,
    ) -> list[int]:
        """Determine optimal split points from available positions."""
        # Calculate minimum number of parts needed to ensure no part exceeds max_part_size
        min_parts_needed = (
            total_length + max_part_size - 1
        ) // max_part_size  # Ceiling division

        # Calculate ideal number of parts based on target_part_size
        ideal_parts = max(
            min_parts_needed, (total_length + target_part_size - 1) // target_part_size
        )

        logger.debug(
            f"File size: {total_length}, minimum parts needed: {min_parts_needed}, "
            f"ideal parts: {ideal_parts}"
        )

        # Calculate the ideal part size
        ideal_part_size = total_length / ideal_parts

        # Strategy: Try to create roughly equal parts using the identified positions
        split_points = [0]  # Start with the beginning of the file
        next_ideal_position = ideal_part_size

        while (
            next_ideal_position < total_length - min_part_size
        ):  # Ensure last part is not too small
            # Find the position closest to the ideal position
            best_position = None
            best_distance = float("inf")

            for pos in usable_positions:
                # Skip positions we've already passed
                if pos <= split_points[-1]:
                    continue

                # Skip positions that would create a part smaller than min_part_size
                if pos - split_points[-1] < min_part_size:
                    continue

                # Skip positions that would create a part larger than max_part_size
                if pos - split_points[-1] > max_part_size:
                    continue

                # Calculate distance to ideal position
                distance = abs(pos - (split_points[-1] + ideal_part_size))

                if distance < best_distance:
                    best_distance = distance
                    best_position = pos

            # If we found a suitable position, use it
            if best_position is not None:
                split_points.append(best_position)
                next_ideal_position = split_points[-1] + ideal_part_size
            else:
                # If we can't find a suitable position, try to find any position that works
                for pos in sorted(usable_positions):
                    if (
                        pos > split_points[-1]
                        and pos - split_points[-1] >= min_part_size
                    ):
                        split_points.append(pos)
                        next_ideal_position = split_points[-1] + ideal_part_size
                        logger.warning(f"Using fallback split point at position {pos}")
                        break
                else:
                    # If we still can't find a suitable position, we have to stop
                    logger.warning("Could not find suitable positions for splitting")
                    break

        # Add the end of the file if it's not already included
        if split_points[-1] != total_length:
            split_points.append(total_length)

        return split_points
