import re
import xml.etree.ElementTree as ET
from pathlib import Path
from loguru import logger

# logger.configure(handlers=[{"sink": "sys.stderr", "level": "INFO"}])


class TtmlToTxtConverter:
    """
    Конвертирует TTML-файлы в чистый текстовый формат.
    Использует ElementTree для парсинга, с фоллбэком на regex очистку при ошибках.
    """

    def __init__(self):
        self.converted_count = 0

    @staticmethod
    def _parse_ttml_content(ttml_content: str) -> str:
        """
        Извлекает текст из TTML, используя ElementTree с фолбэком на regex.
        Аналогично SubtitleParser._parse_ttml_subtitle, но адаптировано для standalone использования.
        """
        try:
            content_bytes = (
                ttml_content.encode("utf-8")
                if isinstance(ttml_content, str)
                else ttml_content
            )
            root = ET.fromstring(content_bytes)
            ns = {"tt": "http://www.w3.org/ns/ttml"}
            texts = [
                "".join(p.itertext()).strip()
                for p in root.findall(".//tt:p", ns)
                if "".join(p.itertext()).strip()
            ]
            if texts:
                return "\n".join(texts).strip()

            logger.warning("No text found in TTML using namespace. Trying without.")
            texts = [
                elem.text.strip()
                for elem in root.iter()
                if elem.text and elem.text.strip()
            ]
            return "\n".join(texts).strip()

        except (ET.ParseError, Exception) as e:
            logger.warning(
                f"Could not parse TTML with ElementTree ({type(e).__name__}). Falling back to regex."
            )
            content_str = (
                ttml_content.decode("utf-8")
                if isinstance(ttml_content, bytes)
                else ttml_content
            )
            clean_text = re.sub(r"<[^>]+>", " ", content_str)
            return re.sub(r"\s+", " ", clean_text).strip()

    def convert_file(self, ttml_file_path: Path) -> Path | None:
        """
        Конвертирует один TTML-файл в TXT.
        Сохраняет TXT-файл рядом с оригинальным TTML-файлом.
        """
        if not ttml_file_path.is_file():
            logger.error(f"Файл не найден: {ttml_file_path}")
            return None

        try:
            ttml_content = ttml_file_path.read_text(encoding="utf-8")
            converted_text = self._parse_ttml_content(ttml_content)

            if not converted_text:
                logger.warning(
                    f"Конвертированный текст пуст для файла: {ttml_file_path.name}"
                )
                return None

            txt_file_path = ttml_file_path.parent / (ttml_file_path.stem + ".txt")
            txt_file_path.write_text(converted_text, encoding="utf-8")
            logger.info(
                f"Успешно конвертирован {ttml_file_path.name} в {txt_file_path.name}"
            )
            return txt_file_path
        except Exception as e:
            logger.error(f"Ошибка при конвертации файла {ttml_file_path.name}: {e}")
            return None

    def convert_directory(self, directory_path: Path) -> int:
        """
        Конвертирует все TTML-файлы в указанной директории в TXT.
        """
        if not directory_path.is_dir():
            logger.error(f"Директория не найдена: {directory_path}")
            return 0

        converted_count = 0
        for ttml_file in directory_path.glob("*.ttml"):
            if self.convert_file(ttml_file):
                converted_count += 1
        logger.info(
            f"Завершена конвертация. Конвертировано {converted_count} TTML файлов в TXT."
        )
        return converted_count
