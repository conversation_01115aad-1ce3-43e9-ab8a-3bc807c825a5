from pathlib import Path
import srt
from loguru import logger


class SrtToTxtConverter:
    """
    A utility class to convert SRT subtitle files to plain TXT files.
    """

    def __init__(self):
        logger.info("SrtToTxtConverter initialized.")

    def convert_file(self, srt_file_path: Path) -> Path | None:
        """
        Converts a single SRT file to a TXT file.

        Args:
            srt_file_path: The path to the input SRT file.

        Returns:
            The path to the generated TXT file, or None if conversion fails.
        """
        if not srt_file_path.exists():
            logger.warning(f"SRT file not found: {srt_file_path}")
            return None
        if not srt_file_path.is_file():
            logger.warning(f"Path is not a file: {srt_file_path}")
            return None

        try:
            with open(srt_file_path, "r", encoding="utf-8") as f:
                srt_content = f.read()

            # Parse SRT content
            # The srt library expects content to be iterable, so we pass it as a list
            # of lines for older versions or just the string for newer versions.
            # Let's ensure compatibility.
            try:
                subs = list(srt.parse(srt_content))
            except Exception as e:
                logger.error(f"Error parsing SRT content from {srt_file_path}: {e}")
                return None

            # Extract text and join them
            text_content = "\n".join(sub.content for sub in subs)

            txt_file_path = srt_file_path.with_suffix(".txt")
            with open(txt_file_path, "w", encoding="utf-8") as f:
                f.write(text_content)

            logger.info(f"Converted {srt_file_path} to {txt_file_path}")
            return txt_file_path
        except Exception as e:
            logger.error(f"Error converting {srt_file_path}: {e}")
            return None

    def convert_directory(self, directory_path: Path) -> int:
        """
        Converts all SRT files in a given directory to TXT files.

        Args:
            directory_path: The path to the directory containing SRT files.

        Returns:
            The number of SRT files successfully converted.
        """
        if not directory_path.exists() or not directory_path.is_dir():
            logger.error(f"Directory not found or is not a directory: {directory_path}")
            return 0

        converted_count = 0
        for srt_file in directory_path.glob("*.srt"):
            if self.convert_file(srt_file):
                converted_count += 1
        logger.info(
            f"Finished converting SRT files in directory {directory_path}. Total converted: {converted_count}"
        )
        return converted_count
