from client.utils.operations.base_operation import BaseOperation
from client.utils.mergers.file_merger import FileMerger
from loguru import logger
import questionary
import sys
from questionary import Style


class FileMergingOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Объединение текстовых файлов ###")

        extension = questionary.text(
            "Введите расширение файлов для объединения (например, txt, md, py):",
            default="txt",
            style=self.custom_style,
        ).ask()
        if extension is None:
            logger.info("Операция отменена пользователем.")
            sys.exit(0)
        if not extension:
            logger.error("Расширение файла не может быть пустым.")
            sys.exit(1)

        directory = self.get_directory_input(
            "Введите директорию для поиска и объединения файлов (обязательно):"
        )

        prefix = questionary.text(
            "Введите префикс для пути файла (опционально, например, 'my_project/'):",
            default="",
            style=self.custom_style,
        ).ask()
        if prefix is None:
            prefix = ""

        self.confirm_execution(
            "Выполнить поиск и объединение файлов с указанными параметрами?"
        )

        merger = FileMerger()
        merger.merge_files(directory, extension, prefix if prefix else None)
        logger.info("Объединение файлов завершено.")
