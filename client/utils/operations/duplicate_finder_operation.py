from client.utils.operations.base_operation import BaseOperation
from client.utils.duplicate.finder import DuplicateFinder
from loguru import logger
from questionary import Style


class DuplicateFinderOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Поиск дубликатов ###")
        directory = self.get_directory_input(
            "Введите директорию для поиска дубликатов (текущая директория: .):"
        )

        self.confirm_execution("Начать поиск дубликатов?")

        finder = DuplicateFinder(directory)
        finder.find_duplicates()
        logger.info("Поиск дубликатов завершен.")
