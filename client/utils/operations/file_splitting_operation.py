from client.utils.operations.base_operation import BaseOperation
from client.utils.splitters import (
    DelimiterSplitter,
    HeuristicSplitter,
    SplitConfig,
    process_directory,
)
from loguru import logger
import questionary
import sys
from questionary import Style


class FileSplittingOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def get_file_patterns_for_mode(self, mode: str) -> list[str]:
        if mode == "heuristic":
            return ["*.txt", "*.md"]
        elif mode == "delim":
            return ["*.md", "*.txt"]
        else:
            return ["*.md", "*.txt"]

    def create_splitter(
        self, mode: str, config: SplitConfig, delimiter_pattern: str | None = None
    ):
        if mode == "heuristic":
            return HeuristicSplitter(config)
        elif mode == "delim":
            return DelimiterSplitter(config, delimiter_pattern)
        else:
            raise ValueError(f"Unknown splitting mode: {mode}")

    def run(self) -> None:
        logger.info("### Настройка параметров разделения файлов ###")

        directory = self.get_directory_input(
            "Введите директорию для обработки файлов (текущая директория: .):"
        )

        mode = questionary.select(
            "Выберите стратегию деления:",
            choices=["heuristic", "delim"],
            default="delim",
            style=self.custom_style,
        ).ask()

        pattern_choice = questionary.confirm(
            "Использовать тип файлов по умолчанию для выбранной стратегии?",
            default=True,
            style=self.custom_style,
        ).ask()

        patterns = []
        if pattern_choice:
            patterns = self.get_file_patterns_for_mode(mode)
            logger.info(f"Используемые типы файлов: {', '.join(patterns)}")
        else:
            custom_pattern = questionary.text(
                "Введите шаблон файлов (например, *.md, *.txt):",
                style=self.custom_style,
            ).ask()
            if custom_pattern:
                patterns = [custom_pattern]
            else:
                logger.error("Шаблон файлов не может быть пустым.")
                sys.exit(1)

        max_target = questionary.text(
            "Введите максимальный размер файла для разделения в символах (по умолчанию: 250000):",
            default="250000",
            validate=lambda text: text.isdigit() or "Пожалуйста, введите число.",
            style=self.custom_style,
        ).ask()
        max_target = int(max_target)

        english_multiplier = questionary.text(
            "Введите множитель размера для определения английского текста (по умолчанию: 1.8):",
            default="1.8",
            validate=lambda text: text.replace(".", "", 1).isdigit()
            or "Пожалуйста, введите число с плавающей точкой.",
            style=self.custom_style,
        ).ask()
        english_multiplier = float(english_multiplier)

        delimiter_pattern = None
        if mode == "delim":
            delimiter_pattern = questionary.text(
                r"Введите пользовательский шаблон разделителя (по умолчанию: \n\s*---\s*\n):",
                default=r"\n\s*---\s*\n",
                style=self.custom_style,
            ).ask()

        logger.info("### Сводка выбранных параметров ###")
        logger.info(f"Директория: {directory}")
        logger.info(f"Стратегия деления: {mode}")
        logger.info(f"Типы файлов: {', '.join(patterns)}")
        logger.info(f"Максимальный размер файла: {max_target} символов")
        logger.info(f"Множитель английского текста: {english_multiplier}")
        if delimiter_pattern:
            logger.info(f"Шаблон разделителя: {delimiter_pattern}")

        self.confirm_execution("Выполняем разделение файлов с указанными параметрами?")

        logger.info(f"Using splitting mode: {mode}")
        logger.info(f"Processing directory: {directory}")
        logger.info(f"File patterns: {patterns}")
        logger.info(f"Max file size: {max_target} characters")

        config = SplitConfig(
            max_file_size=max_target,
            english_multiplier=english_multiplier,
        )

        try:
            splitter = self.create_splitter(mode, config, delimiter_pattern)
        except ValueError as e:
            logger.error(str(e))
            sys.exit(1)

        total_files_processed = 0
        total_files_split = 0

        for pattern in patterns:
            logger.info(f"Processing files matching pattern: {pattern}")
            files_processed, files_split = process_directory(
                splitter, directory, pattern
            )
            total_files_processed += files_processed
            total_files_split += files_split

            if files_processed > 0:
                logger.info(
                    f"Pattern {pattern}: Processed {files_processed} files, split {files_split} files"
                )

        if total_files_processed == 0:
            logger.warning("No files were found matching the specified patterns")
        else:
            logger.info(
                f"Final Summary: Processed {total_files_processed} files, "
                f"split {total_files_split} files"
            )

            if total_files_split > 0:
                logger.info(
                    "Часть файлов была разделена. Проверьте директорию для"
                    "разделенных файлов."
                )
            else:
                logger.info("Ни один файл не требовал разделения.")
