from client.utils.operations.base_operation import BaseOperation
from client.utils.converters.srt_to_txt_converter import SrtToTxtConverter
from loguru import logger
from pathlib import Path
from questionary import Style


class SrtToTxtOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Конвертация SRT в TXT ###")
        directory = self.get_directory_input(
            "Введите директорию с SRT файлами (текущая директория: .):"
        )

        self.confirm_execution("Начать конвертацию SRT в TXT?")

        logger.info(f"Starting SRT to TXT conversion in directory: {directory}")
        converter = SrtToTxtConverter()
        converted_count = converter.convert_directory(Path(directory))
        logger.info(f"Total SRT files converted: {converted_count}")
