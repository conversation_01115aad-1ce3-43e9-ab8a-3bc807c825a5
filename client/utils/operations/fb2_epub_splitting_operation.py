from pathlib import Path
import sys

import questionary
from questionary import Style
from loguru import logger

from client.utils.operations.base_operation import BaseOperation
from client.utils.splitters.fb2_chapter_splitter import Fb2ChapterSplitter
from client.utils.splitters.epub_chapter_splitter import EpubChapterSplitter
from client.utils.splitters.base import SplitConfig
# from client.utils.splitters.epub_chapter_splitter import EpubChapterSplitter # Will be added later


class Fb2EpubSplittingOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Разделение FB2/EPUB по главам ###")

        input_dir = self.get_directory_input(
            "Введите путь к директории с файлами FB2/EPUB для разделения:",
            default_dir="",
        )
        # Removed output_dir prompt and creation as per user request
        # output_dir = self.get_directory_input(
        #     "Введите путь к директории для сохранения разделенных файлов:",
        #     default_dir="./output_fb2_epub",
        # )

        # Path(output_dir).mkdir(parents=True, exist_ok=True)

        file_type = questionary.select(
            "Выберите тип файла для разделения:",
            choices=["FB2", "EPUB"],
            default="FB2",
            style=self.custom_style,
        ).ask()

        if file_type is None:
            logger.info("Операция отменена пользователем.")
            sys.exit(0)

        # Confirm execution with selected parameters
        if not self.confirm_execution(
            f"Провести разделение {file_type} файлов из '{input_dir}' с выбранными параметрами?"
        ):
            sys.exit(0)

        if file_type == "FB2":
            splitter = Fb2ChapterSplitter(SplitConfig())
            files_to_process = list(Path(input_dir).glob("*.fb2"))
            logger.info(f"Найдено {len(files_to_process)} файлов FB2.")
            for fb2_file in files_to_process:
                logger.info(f"Обработка файла: {fb2_file.name}")
                try:
                    splitter.split_file(
                        fb2_file, Path(input_dir)
                    )  # Pass input_dir as output_base_dir
                    logger.info(f"Файл {fb2_file.name} успешно разделен.")
                except Exception as e:
                    logger.error(f"Ошибка при разделении файла {fb2_file.name}: {e}")
        elif file_type == "EPUB":
            splitter = EpubChapterSplitter(SplitConfig())
            files_to_process = list(Path(input_dir).glob("*.epub"))
            logger.info(f"Найдено {len(files_to_process)} файлов EPUB.")
            for epub_file in files_to_process:
                logger.info(f"Обработка файла: {epub_file.name}")
                try:
                    splitter.split_file(
                        epub_file, Path(input_dir)
                    )  # Pass input_dir as output_base_dir
                    logger.info(f"Файл {epub_file.name} успешно разделен.")
                except Exception as e:
                    logger.error(f"Ошибка при разделении файла {epub_file.name}: {e}")
        else:
            logger.error("Неизвестный тип файла.")
            sys.exit(1)

        logger.info(f"Разделенные файлы сохранены в поддиректориях внутри: {input_dir}")
