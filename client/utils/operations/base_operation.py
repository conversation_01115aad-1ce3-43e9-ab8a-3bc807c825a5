from abc import ABC, abstractmethod
from pathlib import Path
import sys

import questionary
from questionary import Style
from loguru import logger


class BaseOperation(ABC):
    def __init__(self, custom_style: Style):
        self.custom_style = custom_style

    def validate_directory(self, directory: str) -> bool:
        directory_path = Path(directory)
        if not directory_path.exists():
            logger.error(f"Directory {directory} does not exist")
            return False
        if not directory_path.is_dir():
            logger.error(f"{directory} is not a directory")
            return False
        return True

    def get_directory_input(self, prompt: str, default_dir: str = "") -> str:
        while True:
            directory = questionary.text(
                prompt,
                default=default_dir,
                style=self.custom_style,
            ).ask()
            if directory is None:  # User cancelled
                logger.info("Операция отменена пользователем.")
                sys.exit(0)
            if not directory:
                logger.warning(
                    "Директория не может быть пустой. Пожалуйста, введите путь."
                )
                continue
            if self.validate_directory(directory):
                return directory
            else:
                logger.warning(
                    "Указанная директория не существует или не является директорией. Попробуйте еще раз."
                )

    def confirm_execution(self, prompt: str) -> bool:
        confirm = questionary.confirm(
            prompt, default=True, style=self.custom_style
        ).ask()
        if not confirm:
            logger.info("Операция отменена пользователем.")
            sys.exit(0)
        return True

    @abstractmethod
    def run(self) -> None:
        pass
