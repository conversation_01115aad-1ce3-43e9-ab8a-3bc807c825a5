from pathlib import Path
from loguru import logger


class FileMerger:
    def __init__(self, output_filename: str = "result.md"):
        self.output_filename = output_filename

    def merge_files(
        self, directory: str, extension: str, prefix: str | None = None
    ) -> None:
        directory_path = Path(directory)
        output_path = directory_path.parent / self.output_filename

        merged_content = []
        for file_path in directory_path.rglob(f"*.{extension}"):
            try:
                relative_path = file_path.relative_to(directory_path)
                display_path = (
                    f"{prefix}/{relative_path}" if prefix else str(relative_path)
                )
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                merged_content.append(
                    f"------{display_path}-------\n{content}\n-----------------------------------"
                )
                logger.info(f"Merged file: {display_path}")
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {e}")

        if merged_content:
            with open(output_path, "w", encoding="utf-8") as f:
                f.write("\n".join(merged_content))
            logger.info(f"All files merged into {output_path}")
        else:
            logger.warning(
                f"No files with extension '.{extension}' found in {directory}"
            )
