---
description: Инструкция для точечной отладки
globs: 
alwaysApply: false
---
Follow this logic **strictly**:

1.  **Assess Complexity:** Look at the function I want to test.
    *   **Is the test logic simple?** (under ~8 lines, no complex setup, one clear goal) -> **USE METHOD 1: INLINE TEST.**
    *   **Is the test logic complex?** (multiple steps, requires imports from different files, needs mock objects, or is longer than ~8 lines) -> **USE METHOD 2: TEMPORARY FILE TEST.**

2.  **Execute the Chosen Method:** Provide the exact commands for the chosen method.

---

### **METHOD 1: Inline Test (for quick checks)**

**Your Action:** Generate a single `uv run python -c "..."` command.

**Mantra:** "One function, one check, one line."

**Example Prompt:** "Check `get_video_id` from `utils.py`."

**Your Response:**

Here's a quick inline test. Copy and paste it into your terminal:
```bash
uv run python -c "
import sys
sys.path.append('.')
from utils import get_video_id
url = 'https://youtu.be/dQw4w9WgXcQ'
video_id = get_video_id(url)
assert video_id == 'dQw4w9WgXcQ'
print(f'✅ Parsed {url} -> {video_id}')
"
```

---

### **METHOD 2: Temporary File Test (for complex debugging)**

**Your Action:** Generate a **three-step command block** to create, run, and clean up a temporary test file.

**Mantra:** "Create, run, destroy. Leave no trace."

**Example Prompt:** "I need to test the `process_and_save_summary` function in `tasks.py`. It needs a mock database session."

**Your Response:**

This requires a bit more setup. Let's use a temporary test file. Execute these commands one by one:

**Step 1: Create the test file.**
```bash
cat << 'EOF' > test_temp_summary.py
import sys
import asyncio
from unittest.mock import AsyncMock

sys.path.append('.')
# Imagine this is a complex function we need to test
from tasks import process_and_save_summary 

async def run_test():
    print("🧪 Running complex summary test...")
    # Mocking a database session
    mock_db_session = AsyncMock()
    mock_db_session.commit.return_value = None
    
    text = "This is a long text that needs summarizing."
    task_id = "task-123"
    
    # Calling the function with the mock
    await process_and_save_summary(mock_db_session, task_id, text)
    
    # We can check if our mock was called correctly
    mock_db_session.add.assert_called_once()
    mock_db_session.commit.assert_awaited_once()
    print("✅ Mock DB session was used as expected.")

if __name__ == "__main__":
    asyncio.run(run_test())
EOF
```

**Step 2: Run the test.**
```bash
uv run python test_temp_summary.py
```

**Step 3: Clean up the file.**
```bash
rm test_temp_summary.py
```
This approach keeps your project directory clean while allowing for complex, multi-line debugging scripts.